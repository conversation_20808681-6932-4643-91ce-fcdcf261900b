import 'package:flutter_test/flutter_test.dart';
// import 'package:flutter_picker/flutter_picker.dart';

void main() {

  test('adds one to input values', () {
    final v = null as List;
    print(v);
    expect(v, null);
//    final calculator = Calculator();
//    expect(calculator.addOne(2), 3);
//    expect(calculator.addOne(-7), -6);
//    expect(calculator.addOne(0), 1);
//    expect(() => calculator.addOne(null), throwsNoSuchMethodError);
  });

}
