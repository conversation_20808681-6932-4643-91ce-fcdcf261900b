import 'package:flutter/material.dart';
import 'package:flutter_slidable_plus_plus/flutter_slidable_plus_plus.dart';

/// 安全控制左右滑块的 Slidable，
/// 只能单侧打开，反向滑动时只能关闭当前展开菜单，不能直接切换。
class SafeDualSlidable extends StatefulWidget {
  final Widget child;
  final ActionPane? startActionPane;
  final ActionPane? endActionPane;
  final SlidableController? controller;
  final String? groupTag;
  final Key? slidableKey;

  const SafeDualSlidable({
    Key? key,
    required this.child,
    this.startActionPane,
    this.endActionPane,
    this.controller,
    this.groupTag,
    this.slidableKey,
  }) : super(key: key);

  @override
  State<SafeDualSlidable> createState() => _SafeDualSlidableState();
}

class _SafeDualSlidableState extends State<SafeDualSlidable> with TickerProviderStateMixin{
  late SlidableController _controller;

  ActionPaneType _currentPaneType = ActionPaneType.none;

  bool get _isLeftOpen => _currentPaneType == ActionPaneType.start;
  bool get _isRightOpen => _currentPaneType == ActionPaneType.end;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? SlidableController(this);

    _currentPaneType = _controller.actionPaneType.value;

    _controller.actionPaneType.addListener(() {
      setState(() {
        _currentPaneType = _controller.actionPaneType.value;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onHorizontalDragUpdate: (details) {
        final dx = details.delta.dx;

        if (_isRightOpen && dx > 0) {
          // 右侧菜单已打开，右滑则关闭，不允许直接打开左侧菜单
          _controller.close();
        } else if (_isLeftOpen && dx < 0) {
          // 左侧菜单已打开，左滑则关闭，不允许直接打开右侧菜单
          _controller.close();
        }
      },
      child: Slidable(
        key: widget.slidableKey,
        controller: _controller,
        groupTag: widget.groupTag,
        child: widget.child,
        startActionPane: _isRightOpen ? null : widget.startActionPane,
        endActionPane: _isLeftOpen ? null : widget.endActionPane,
      ),
    );
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }
}

