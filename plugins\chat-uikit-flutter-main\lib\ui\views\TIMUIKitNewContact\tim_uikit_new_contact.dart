import 'dart:async';
import 'package:azlistview_all_platforms/azlistview_all_platforms.dart';
import 'package:flutter/material.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_application.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_application.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/life_cycle/new_contact_life_cycle.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_friendship_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/avatar.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';

typedef NewContactItemBuilder = Widget Function(
    BuildContext context, UnifiedContactApplicationItem applicationInfo);

class ContactItem extends ISuspensionBean {
  final UnifiedContactApplicationItem applicationInfo;
  String tag;

  ContactItem(this.applicationInfo, {this.tag = '#'});

  @override
  String getSuspensionTag() => tag;
}

class UnifiedContactApplicationItem {
  final V2TimFriendApplication? friendApplication;
  final V2TimGroupApplication? groupApplication;

  UnifiedContactApplicationItem.friend(this.friendApplication)
      : groupApplication = null;

  UnifiedContactApplicationItem.group(this.groupApplication)
      : friendApplication = null;

  bool get isFriendApplication => friendApplication != null;

  bool get isGroupApplication => groupApplication != null;

  String get userID =>
      friendApplication?.userID ?? groupApplication?.fromUser ?? "";

  String get nickname =>
      friendApplication?.nickname ?? groupApplication?.fromUserNickName ?? "";

  String get faceUrl =>
      friendApplication?.faceUrl ?? groupApplication?.fromUserFaceUrl ?? "";
}

class Hbouncer {
  final Duration delay;
  Timer? _timer;

  int secound = 0;

  Hbouncer({required this.delay});

  void run(VoidCallback action) {
    if (_timer != null) {
      _timer!.cancel();
      _timer = null;
      secound = 0;
    }
    secound++;
    print("HDebouncer ----- $secound");
    _timer = Timer(delay, action);
  }

  void cancle() {
    if (_timer != null) {
      _timer!.cancel();
      _timer = null;
      secound = 0;
    }
  }
}

class TIMUIKitNewContact extends StatefulWidget {
  final void Function(UnifiedContactApplicationItem applicationInfo)? onAccept;
  final void Function(UnifiedContactApplicationItem applicationInfo)? onRefuse;
  final Widget Function(BuildContext context)? emptyBuilder;
  final NewContactItemBuilder? itemBuilder;
  final NewContactLifeCycle? lifeCycle;
  final List<V2TimGroupApplication>? extGroups;

  const TIMUIKitNewContact({
    Key? key,
    this.lifeCycle,
    this.onAccept,
    this.onRefuse,
    this.emptyBuilder,
    this.itemBuilder,
    this.extGroups,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _TIMUIKitNewContactState();
}

class _TIMUIKitNewContactState extends TIMUIKitState<TIMUIKitNewContact> {
  late TUIFriendShipViewModel model = serviceLocator<TUIFriendShipViewModel>();
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _controller = TextEditingController();
  List<ContactItem> _contactList = [];

  Hbouncer hbouncer = Hbouncer(delay: Duration(milliseconds: 1000));

  @override
  void initState() {
    super.initState();
  }

  // void _prepareContactList(List<V2TimFriendApplication> rawList) {
  //   _contactList = rawList.map((app) {
  //     final name = TencentUtils.checkString(app.nickname) ??
  //         TencentUtils.checkString(app.userID) ??
  //         '';
  //     final pinyin = PinyinHelper.getPinyinE(name).toUpperCase();
  //     final tag = pinyin.isNotEmpty ? pinyin[0] : '#';
  //     return ContactItem(app, tag: RegExp('[A-Z]').hasMatch(tag) ? tag : '#');
  //   }).toList();
  //
  //   SuspensionUtil.sortListBySuspensionTag(_contactList);
  //   SuspensionUtil.setShowSuspensionStatus(_contactList);
  // }

  void _prepareContactList(List<UnifiedContactApplicationItem> rawList) {
    _contactList = rawList.map((item) {
      final name = item.nickname.isNotEmpty ? item.nickname : item.userID;
      final pinyin = PinyinHelper.getPinyinE(name).toUpperCase();
      final tag = pinyin.isNotEmpty ? pinyin[0] : '#';
      return ContactItem(item, tag: RegExp('[A-Z]').hasMatch(tag) ? tag : '#');
    }).toList();

    SuspensionUtil.sortListBySuspensionTag(_contactList);
    SuspensionUtil.setShowSuspensionStatus(_contactList);
  }

  Future<List<UnifiedContactApplicationItem>> loadAllApplications() async {
    List<UnifiedContactApplicationItem> finalList = [];

    // 好友申请
    final rawList = model.friendApplicationList ?? [];
    print('rawList length: ${rawList.length}');

    // First ensure all items are non-null and of type V2TimFriendApplication
    final friendApplications = rawList.whereType<V2TimFriendApplication>();
    print('filtered friend applications length: ${friendApplications.length}');

    // Create UnifiedContactApplicationItem for each friend application
    finalList.addAll(
        friendApplications.map((f) => UnifiedContactApplicationItem.friend(f)));

    // 群申请
    final groupRes = await TencentImSDKPlugin.v2TIMManager
        .getGroupManager()
        .getGroupApplicationList();
    if (groupRes.code == 0) {
      final groupList = groupRes.data?.groupApplicationList ?? [];
      finalList.addAll(groupList
          .whereType<V2TimGroupApplication>()
          .map((g) => UnifiedContactApplicationItem.group(g)));
    }

    // 扩展群申请(自有)
    finalList.addAll(widget.extGroups
            ?.whereType<V2TimGroupApplication>()
            ?.map((g) => UnifiedContactApplicationItem.group(g)) ??
        []);

    return finalList;
  }

  Widget _itemBuilder(
      BuildContext context, UnifiedContactApplicationItem item) {
    final showName = item.nickname.isNotEmpty ? item.nickname : item.userID;
    final faceUrl = item.faceUrl;
    final option5 = '';

    ///国际化
    var invist =
        TIM_t_para("邀请{{option5}}加入群组", "邀请$option5加入群组")(option5: option5);

    return InkWell(
      onTap: () {},
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(9),
          color: Colors.white,
        ),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        margin: EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            SizedBox(
              width: 40,
              height: 40,
              child: Avatar(
                faceUrl: faceUrl,
                showName: showName,
                borderRadius: BorderRadius.circular(50),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(showName,
                      style: TextStyle(fontSize: 14, color: Colors.black)),
                  if (item.isGroupApplication)
                    Text("$invist: ${item.groupApplication?.groupID}",
                        style: TextStyle(fontSize: 12, color: Colors.grey)),
                ],
              ),
            ),
            _buildStatus(item)
          ],
        ),
      ),
    );
  }

  NewContactItemBuilder _getItemBuilder() {
    return widget.itemBuilder ?? _itemBuilder;
  }

  _buildStatus(UnifiedContactApplicationItem item) {
    if (item.isGroupApplication) {
      if (item.groupApplication?.handleResult == 1) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(TIM_t("已同意"), style: TextStyle(color: Color(0xFFA0A0A0)))
          ],
        );
      } else if (item.groupApplication?.handleResult == 2) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(TIM_t("已拒绝"), style: TextStyle(color: Color(0xFFA0A0A0)))
          ],
        );
      }
    }
    return Row(
      children: [
        InkWell(
          onTap: () async {
            if (item.isFriendApplication) {
              await model.acceptFriendApplication(
                  item.userID, item.friendApplication!.type);
            } else if (item.isGroupApplication) {
              if (widget.onAccept != null) {
                widget.onAccept!(item);
              } else {
                final res = await TencentImSDKPlugin.v2TIMManager
                    .getGroupManager()
                    .acceptGroupApplication(
                        application: item.groupApplication!,
                        reason: TIM_t("已加入"),
                        groupID: item.groupApplication?.groupID ?? "",
                        fromUser: item.groupApplication?.fromUser ?? "",
                        toUser: item.groupApplication?.toUser ?? "");
              }
            }

            model.loadData();
            setState(() {}); // 刷新
          },
          child: Text(TIM_t("同意"), style: TextStyle(color: Color(0xFF1DAA61))),
        ),
        SizedBox(width: 10),
        InkWell(
          onTap: () async {
            if (item.isFriendApplication) {
              await model.refuseFriendApplication(
                  item.userID, item.friendApplication!.type);
            } else if (item.isGroupApplication) {
              if (widget.onRefuse != null) {
                widget.onRefuse!(item);
              } else {
                // final res= await TencentImSDKPlugin.v2TIMManager
                //   .getGroupManager()
                //   .refuseGroupApplication(
                //       application: item.groupApplication!,
                //       groupID: item.groupApplication?.groupID ?? '',
                //       fromUser: item.groupApplication?.fromUser ?? '',
                //       toUser: item.groupApplication?.toUser ?? '',
                //       addTime: item.groupApplication?.addTime ?? 0,
                //       type: GroupApplicationTypeEnum
                //           .values[item.groupApplication?.type ?? 0]);
                // if (res.code == 0) {
                //  print('-=-=-打印=--=');
                // }
              }
            }

            model.loadData();
            setState(() {});
          },
          child: Text(TIM_t("拒绝"), style: TextStyle(color: Color(0xFF1DAA61))),
        ),
      ],
    );
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    return MultiProvider(
      providers: [ChangeNotifierProvider.value(value: model)],
      builder: (context, _) {
        final model = Provider.of<TUIFriendShipViewModel>(context);
        model.newContactLifeCycle = widget.lifeCycle;

        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 10, 16, 10),
              child: SizedBox(
                height: 27,
                child: TextField(
                  style: TextStyle(fontSize: 12),
                  focusNode: _focusNode,
                  controller: _controller,
                  onChanged: (value) {
                    if (value.isNotEmpty)
                      hbouncer.run(() {
                        setState(() {}); // 实时搜索刷新
                      });
                  },
                  textInputAction: TextInputAction.search,
                  onSubmitted: (_) {
                    _focusNode.requestFocus();
                    setState(() {});
                  },
                  decoration: InputDecoration(
                    prefixIcon: IconButton(
                      icon: Image.asset(
                        'assets/images/png/chat/icon_search.png',
                        width: 12,
                        height: 12,
                        color: Color(0xFF7C7B80),
                      ),
                      onPressed: () {},
                    ),
                    suffixIcon: IconButton(
                      icon: Image.asset(
                        'images/close.png',
                        package: 'tencent_cloud_chat_uikit',
                      ),
                      onPressed: () {
                        _controller.text = '';
                        setState(() {});
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(9),
                      borderSide: const BorderSide(
                        width: 0,
                        style: BorderStyle.none,
                      ),
                    ),
                    contentPadding: EdgeInsets.zero,
                    hintStyle: TextStyle(
                      color: hexToColor('646464'),
                      fontSize: 12,
                    ),
                    labelStyle: TextStyle(
                      color: hexToColor('646464'),
                      fontSize: 12,
                    ),
                    fillColor: hexToColor('ECECEC'),
                    filled: true,
                    hintText: TIM_t("搜索") + 'ID',
                  ),
                ),
              ),
            ),
            Expanded(
              child: FutureBuilder<List<UnifiedContactApplicationItem>>(
                future: loadAllApplications(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  var newContactList = snapshot.data ?? [];

                  if (_controller.text.isNotEmpty) {
                    newContactList = newContactList
                        .where((e) =>
                    e.nickname.contains(_controller.text) ||
                        e.userID.contains(_controller.text))
                        .toList();
                  }

                  _prepareContactList(newContactList);

                  if (_contactList.isEmpty) {
                    return widget.emptyBuilder?.call(context) ??
                        Center(child: Text(TIM_t("暂无新联系人")));
                  }

                  return AzListView(
                    data: _contactList,
                    itemCount: _contactList.length,
                    itemBuilder: (context, index) {
                      final item = _contactList[index];
                      return _getItemBuilder()(context, item.applicationInfo);
                    },
                    indexBarData: SuspensionUtil.getTagIndexList(_contactList),
                    indexBarOptions: IndexBarOptions(
                      textStyle: TextStyle(fontSize: 12, color: Colors.black),
                      indexHintTextStyle: TextStyle(fontSize: 24, color: Colors.white),
                      indexHintDecoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    susItemBuilder: (context, index) {
                      final tag = _contactList[index].getSuspensionTag();
                      return Container(
                        height: 36,
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        margin: EdgeInsets.symmetric(horizontal: 16),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          tag,
                          style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                        ),
                      );
                    },
                  );
                },
              ),
            ),

          ],
        );
      },
    );
  }
}
