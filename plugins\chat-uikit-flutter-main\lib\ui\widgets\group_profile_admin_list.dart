// ignore_for_file: must_be_immutable


import 'package:azlistview_all_platforms/azlistview_all_platforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable_plus_plus/flutter_slidable_plus_plus.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_member_role.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart' if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_group_member_full_info.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/optimize_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/avatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/az_list_view.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/radio_button.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme_view_model.dart';

class GroupProfileAdminList extends StatefulWidget {
  static String AT_ALL_USER_ID = "__kImSDK_MesssageAtALL__";
  final List<V2TimGroupMemberFullInfo?> memberList;
  final Function(String userID)? removeMember;
  final Function(V2TimGroupMemberFullInfo info)? removeManageMember;
  final bool isAdminManage;
  final bool canSlideDelete;
  final bool canSelectMember;
  final bool canAtAll;
  final String? groupType;
  final Function(List<V2TimGroupMemberFullInfo> selectedMember)? onSelectedMemberChange;
  final Function(V2TimGroupMemberFullInfo memberInfo, TapDownDetails? tapDetails)? onTapMemberItem;
  final Function()? touchBottomCallBack;
  final int? maxSelectNum;
  Widget? customTopArea;

  GroupProfileAdminList({
    Key? key,
    required this.memberList,
    this.groupType,
    this.removeMember,
    this.removeManageMember,
    this.canSlideDelete = true,
    this.canSelectMember = false,
    this.canAtAll = false,
    this.isAdminManage = false,
    this.onSelectedMemberChange,
    this.onTapMemberItem,
    this.customTopArea,
    this.touchBottomCallBack,
    this.maxSelectNum,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _GroupProfileAdminListState();
}

class _GroupProfileAdminListState extends TIMUIKitState<GroupProfileAdminList> {
  List<V2TimGroupMemberFullInfo> selectedMemberList = [];
  Set<String> canceledAdminUserIDs = {};

  @override
  void initState() {
    super.initState();
    if (widget.isAdminManage) {
      selectedMemberList = widget.memberList
          .where((e) => e?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN)
          .whereType<V2TimGroupMemberFullInfo>()
          .toList();
    }
  }

  _getShowName(V2TimGroupMemberFullInfo? item) {
    final friendRemark = item?.friendRemark ?? "";
    final nameCard = item?.nameCard ?? "";
    final nickName = item?.nickName ?? "";
    final userID = item?.userID ?? "";
    return friendRemark.isNotEmpty
        ? friendRemark
        : nameCard.isNotEmpty
        ? nameCard
        : nickName.isNotEmpty
        ? nickName
        : userID;
  }

  List<ISuspensionBeanImpl> _getShowList(List<V2TimGroupMemberFullInfo?> memberList) {
    final List<ISuspensionBeanImpl> showList = List.empty(growable: true);
    for (var i = 0; i < memberList.length; i++) {
      final item = memberList[i];
      final showName = _getShowName(item);
      if (item?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER || item?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN) {
        showList.add(ISuspensionBeanImpl(memberInfo: item, tagIndex: "@"));
      } else {
        String pinyin = PinyinHelper.getPinyinE(showName);
        String tag = pinyin.substring(0, 1).toUpperCase();
        if (RegExp("[A-Z]").hasMatch(tag)) {
          showList.add(ISuspensionBeanImpl(memberInfo: item, tagIndex: tag));
        } else {
          showList.add(ISuspensionBeanImpl(memberInfo: item, tagIndex: "#"));
        }
      }
    }

    SuspensionUtil.sortListBySuspensionTag(showList);

    if (widget.canAtAll) {
      final canAtGroupType = ["Work", "Public", "Meeting"];
      if (canAtGroupType.contains(widget.groupType)) {
        showList.insert(0, ISuspensionBeanImpl(memberInfo: V2TimGroupMemberFullInfo(userID: GroupProfileAdminList.AT_ALL_USER_ID, nickName: TIM_t("所有人")), tagIndex: ""));
      }
    }

    return showList;
  }

  Widget _buildListItem(BuildContext context, V2TimGroupMemberFullInfo memberInfo) {
    final theme = Provider.of<TUIThemeViewModel>(context).theme;
    final isDesktopScreen = TUIKitScreenUtils.getFormFactor() == DeviceType.Desktop;
    final isGroupMember = memberInfo.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_MEMBER;
    final isAdmin = memberInfo.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN;
    final isOwner = memberInfo.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER;

    final canDelete = widget.isAdminManage ? (isAdmin || isOwner) : widget.canSlideDelete && isGroupMember;
    final canSelect = widget.isAdminManage ? isGroupMember : widget.canSelectMember;

    return Slidable(
        endActionPane: canDelete
            ? ActionPane(motion: const DrawerMotion(), children: [
          SlidableAction(
            onPressed: (_) {
              if (widget.removeMember != null) {
                widget.removeMember!(memberInfo.userID);
              }
              if (widget.isAdminManage && widget.removeManageMember != null) {
                widget.removeManageMember!(memberInfo);
              }
            },
            flex: 1,
            backgroundColor: theme.cautionColor ?? CommonColor.cautionColor,
            autoClose: true,
            label: TIM_t("删除"),
          )
        ])
            : null,
        child: Column(children: [
          ListTile(
            tileColor: Colors.black,
            title: Row(
              children: [
                Container(
                  width: isDesktopScreen ? 30 : 36,
                  height: isDesktopScreen ? 30 : 36,
                  margin: const EdgeInsets.only(right: 10),
                  child: Avatar(
                    faceUrl: memberInfo.faceUrl ?? "",
                    showName: _getShowName(memberInfo),
                    borderRadius: BorderRadius.circular(50),
                    type: 1,
                  ),
                ),
                Text(_getShowName(memberInfo), style: TextStyle(fontSize: isDesktopScreen ? 14 : 12)),
                const Spacer(),
                if (isOwner)
                  _buildRoleTag("群主", theme.textgrey ?? CommonColor.ownerColor, isDesktopScreen)
                else if (isAdmin)
                  _buildRoleTag("管理员", theme.textgrey ?? CommonColor.adminColor, isDesktopScreen),
                if (canSelect || isAdmin)
                  Container(
                    margin: const EdgeInsets.only(right: 10),
                    width: 13,
                    height: 13,
                    child: CheckBoxButton(
                      onChanged: (isChecked) {
                        final isSelected = selectedMemberList.any((e) => e.userID == memberInfo.userID);
                        final isAlreadyAdmin = memberInfo.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN;

                        if (isChecked) {
                          if (widget.maxSelectNum != null && selectedMemberList.length >= widget.maxSelectNum!) {
                            return;
                          }
                          if (!isSelected) {
                            selectedMemberList.add(memberInfo);
                          }
                          if (isAlreadyAdmin) {
                            canceledAdminUserIDs.remove(memberInfo.userID);
                          }
                        } else {
                          selectedMemberList.removeWhere((e) => e.userID == memberInfo.userID);
                          if (isAlreadyAdmin) {
                            canceledAdminUserIDs.add(memberInfo.userID!);
                            widget.removeManageMember?.call(memberInfo); // 降级为普通成员
                          }
                        }

                        widget.onSelectedMemberChange?.call(selectedMemberList);
                        setState(() {});
                      },

                      isChecked: selectedMemberList.any((element) => element.userID == memberInfo.userID),
                    ),
                  ),
              ],
            ),
            onTap: () {
              if (widget.onTapMemberItem != null) {
                widget.onTapMemberItem!(memberInfo, null);
              }
            },
          ),
          Divider(thickness: 1, indent: 74, endIndent: 0, color: theme.weakBackgroundColor, height: 0)
        ]));
  }

  Widget _buildRoleTag(String text, Color color, bool isDesktopScreen) {
    return Container(
      margin: const EdgeInsets.only(left: 5, right: 5),
      padding: const EdgeInsets.fromLTRB(5, 0, 5, 0),
      child: Text(
        TIM_t(text),
        style: TextStyle(color: color, fontSize: isDesktopScreen ? 10 : 12),
      ),
    );
  }

  static Widget getSusItem(BuildContext context, TUITheme theme, String tag, {double susHeight = 40}) {
    if (tag == '@') tag = TIM_t("群主、管理员");
    return Container(
      height: susHeight,
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.only(left: 16.0),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      alignment: Alignment.centerLeft,
      child: Text(tag, softWrap: true, style: TextStyle(fontSize: 12.0, color: hexToColor('6E6E6E'))),
    );
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;
    final isDesktopScreen = TUIKitScreenUtils.getFormFactor() == DeviceType.Desktop;
    final throteFunction = OptimizeUtils.throttle((ScrollNotification notification) {
      final pixels = notification.metrics.pixels;
      final maxScrollExtent = notification.metrics.maxScrollExtent;
      final progress = pixels / maxScrollExtent;
      if (progress >= 0.9 && widget.touchBottomCallBack != null) {
        widget.touchBottomCallBack!();
      }
    }, 300);
    final showList = _getShowList(widget.memberList);
    return Container(
      color: hexToColor('F4F4F4'),
      child: SafeArea(
        child: Column(
          children: [
            widget.customTopArea ?? Container(),
            Expanded(
              child: NotificationListener<ScrollNotification>(
                onNotification: (notification) {
                  throteFunction(notification);
                  return true;
                },
                child: showList.isEmpty
                    ? Center(child: Text(TIM_t("暂无群成员")))
                    : Container(
                  padding: isDesktopScreen ? const EdgeInsets.symmetric(horizontal: 16) : null,
                  child: AZListViewContainer(
                    memberList: showList,
                    susItemBuilder: (context, index) {
                      final model = showList[index];
                      return getSusItem(context, theme, model.getSuspensionTag());
                    },
                    itemBuilder: (context, index) {
                      final model = showList[index];
                      final memberInfo = model.memberInfo as V2TimGroupMemberFullInfo;
                      final isFirst = model.isFirstOfGroup;
                      final isLast = model.isLastOfGroup;
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.vertical(
                            top: isFirst ? const Radius.circular(12) : Radius.zero,
                            bottom: isLast ? const Radius.circular(12) : Radius.zero,
                          ),
                        ),
                        child: _buildListItem(context, memberInfo),
                      );
                    },
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  void handleDonePressed() {
    for (final userId in canceledAdminUserIDs) {
      final memberInfo = widget.memberList.firstWhere(
            (e) => e?.userID == userId,
        orElse: () => null,
      );
      if (memberInfo != null) {
        widget.removeManageMember?.call(memberInfo!);
      }
    }
  }
}

