import 'package:azlistview_all_platforms/azlistview_all_platforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_friend_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_group_member_full_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_status.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_user_status.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_friendship_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/avatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/az_list_view.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/radio_button.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';

class ContactList extends StatefulWidget {
  final List<V2TimFriendInfo> contactList;
  final bool isCanSelectMemberItem;
  final bool isCanSlidableDelete;
  final Function(List<V2TimFriendInfo> selectedMember)?
      onSelectedMemberItemChange;
  final Function()? handleSlidableDelte;
  final Color? bgColor;

  /// tap联系人列表项回调
  final void Function(V2TimFriendInfo item)? onTapItem;

  /// 邀请回调
  final void Function(V2TimFriendInfo item)? onTapJoinLink;

  /// 顶部列表
  final List<TopListItem>? topList;

  /// 顶部列表项构造器
  final Widget? Function(TopListItem item)? topListItemBuilder;

  /// Control if shows the online status for each user on its avatar.
  final bool isShowOnlineStatus;

  final int? maxSelectNum;

  final List<V2TimGroupMemberFullInfo?>? groupMemberList;

  /// the builder for the empty item, especially when there is no contact
  final Widget Function(BuildContext context)? emptyBuilder;

  final String? currentItem;

  final List<V2TimFriendInfo>? defaultSelectedMember;

  const ContactList({
    Key? key,
    required this.contactList,
    this.isCanSelectMemberItem = false,
    this.onSelectedMemberItemChange,
    this.isCanSlidableDelete = false,
    this.handleSlidableDelte,
    this.onTapItem,
    this.onTapJoinLink,
    this.bgColor,
    this.topList,
    this.topListItemBuilder,
    this.isShowOnlineStatus = false,
    this.maxSelectNum,
    this.groupMemberList,
    this.emptyBuilder,
    this.currentItem,
    this.defaultSelectedMember,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ContactListState();
}

class _ContactListState extends TIMUIKitState<ContactList> {
  List<V2TimFriendInfo> selectedMember = [];
  final TUIFriendShipViewModel friendShipViewModel =
      serviceLocator<TUIFriendShipViewModel>();

  @override
  void initState() {
    super.initState();
    if (widget.defaultSelectedMember != null) {
      selectedMember = List.from(widget.defaultSelectedMember!);
    }
  }

  _getShowName(V2TimFriendInfo item) {
    final friendRemark = item.friendRemark ?? "";
    final nickName = item.userProfile?.nickName ?? "";
    final userID = item.userID;
    final showName = nickName != "" ? nickName : userID;
    return friendRemark != "" ? friendRemark : showName;
  }

  List<ISuspensionBeanImpl> _getShowList(List<V2TimFriendInfo> memberList) {
    final List<ISuspensionBeanImpl> showList = List.empty(growable: true);
    for (var i = 0; i < memberList.length; i++) {
      final item = memberList[i];
      final String showName = _getShowName(item);
      String pinyin = PinyinHelper.getPinyinE(showName);
      String tag = pinyin.substring(0, 1).toUpperCase();

      if (showName.startsWith("&")) {
        showList.add(ISuspensionBeanImpl(memberInfo: item, tagIndex: "&"));
      }
      else if (RegExp("[A-Z]").hasMatch(tag)) {
        showList.add(ISuspensionBeanImpl(memberInfo: item, tagIndex: tag));
      }
      else {
        showList.add(ISuspensionBeanImpl(memberInfo: item, tagIndex: "#"));
      }
    }

    SuspensionUtil.sortListBySuspensionTag(showList);

    return showList;
  }

  bool selectedMemberIsOverFlow() {
    if (widget.maxSelectNum == null) {
      return false;
    }

    return selectedMember.length >= widget.maxSelectNum!;
  }

  Widget _buildIsOffice(V2TimFriendInfo item,){
    var isOffice = item.userProfile?.customInfo?[
    'iconauth']
        .toString() ??
        '0';
    if (isOffice == '1') {
      return Padding(
        padding: const EdgeInsets.only(left: 4),
        child: SvgPicture.asset(
          'images/ic_office.svg',
          package: 'tencent_cloud_chat_uikit',
          height: 12,
          width: 12,
        ),
      );
    }
    return SizedBox.shrink();
  }
  Widget _buildItem(
    TUITheme theme,
    V2TimFriendInfo item, {
    required bool isFirst,
    required bool isLast,
    required bool isSingle,
  }) {
    final String showName = _getShowName(item);
    final faceUrl = item.userProfile?.faceUrl ?? "";

    final V2TimUserStatus? onlineStatus = widget.isShowOnlineStatus
        ? friendShipViewModel.userStatusList.firstWhere(
            (element) => element.userID == item.userID,
            orElse: () => V2TimUserStatus(statusType: 0))
        : null;

    bool disabled = false;
    if (widget.groupMemberList != null && widget.groupMemberList!.isNotEmpty) {
      disabled = ((widget.groupMemberList
                  ?.indexWhere((element) => element?.userID == item.userID)) ??
              -1) >
          -1;
    }

    final isDesktopScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;

    // 根据在分组内的位置动态设置圆角
    BorderRadius borderRadius;
    if (isSingle) {
      // 分组内单个项目，四个角都是圆角
      borderRadius = BorderRadius.circular(12);
    } else if (isFirst) {
      // 分组内第一个项目，只有顶部圆角
      borderRadius = const BorderRadius.only(
        topLeft: Radius.circular(12),
        topRight: Radius.circular(12),
      );
    } else if (isLast) {
      // 分组内最后一个项目，只有底部圆角
      borderRadius = const BorderRadius.only(
        bottomLeft: Radius.circular(12),
        bottomRight: Radius.circular(12),
      );
    } else {
      borderRadius = BorderRadius.circular(0);
    }

    // 根据在分组内的位置动态设置边框
    // 单个项目或分组内最后一个项目不显示底部边框
    bool showBottomBorder = !isSingle && !isLast;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 18),
      padding: const EdgeInsets.only(top: 8, left: 16, right: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.only(bottom: 12),
            margin: const EdgeInsets.only(right: 12),
            child: SizedBox(
              height: isDesktopScreen ? 30 : 34,
              width: isDesktopScreen ? 30 : 34,
              child: Avatar(
                  borderRadius: BorderRadius.circular(50),
                  onlineStatus: onlineStatus,
                  faceUrl: faceUrl,
                  showName: showName),
            ),
          ),
          Expanded(
              child: Container(
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
              border: showBottomBorder
                  ? Border(
                      bottom:
                          BorderSide(color: hexToColor("B5B5B5"), width: 0.2))
                  : null,
            ),
            padding: const EdgeInsets.only(top: 10, bottom: 20, right: 28),
            child: Row(
              children: [
                Text(
                  showName.replaceFirst("& ", ""),
                  style: TextStyle(
                      color: Colors.black, fontSize: isDesktopScreen ? 14 : 12),
                ),
                _buildIsOffice(item),
                const SizedBox(
                  width: 4,
                ),

                if (!showName.startsWith("& ") &&
                    item.friendCustomInfo?['Book'] == '1')
                  Container(
                    decoration: BoxDecoration(
                        color: hexToColor('CCFFE4'),
                        borderRadius: BorderRadius.circular(4)),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    child: Text(
                      TIM_t('我的下级'),
                      style: TextStyle(
                          fontSize: 12,
                          color: hexToColor('1DAA61'),
                          fontWeight: FontWeight.w600),
                    ),
                  )
              ],
            ),
          )),
          if (!showName.startsWith("& ") && widget.isCanSelectMemberItem)
            Container(
              margin: const EdgeInsets.only(right: 12, bottom: 8),
              child: CheckBoxButton(
                size: 15,
                disabled: disabled,
                isChecked: selectedMember
                    .any((element) => element.userID == item.userID),
                onChanged: (isChecked) {
                  // if (isChecked) {
                  //   if (selectedMemberIsOverFlow()) {
                  //     selectedMember = [item];
                  //     setState(() {});
                  //     return;
                  //   }
                  //   selectedMember.add(item);
                  // } else {
                  //   selectedMember.remove(item);
                  // }
                  if (isChecked) {
                    if (selectedMemberIsOverFlow()) {
                      selectedMember = [item];
                    } else if (!selectedMember
                        .any((e) => e.userID == item.userID)) {
                      selectedMember.add(item);
                    }
                  } else {
                    selectedMember.removeWhere((e) => e.userID == item.userID);
                  }
                  if (widget.onSelectedMemberItemChange != null) {
                    widget.onSelectedMemberItemChange!(selectedMember);
                  }
                  setState(() {});
                },
              ),
            ),
          if (showName.startsWith("& ")) ...[
            InkWell(
              onTap: () {
                final String showName = _getShowName(item);
                if (showName.startsWith("& ")) {
                  if (widget.onTapJoinLink != null) widget.onTapJoinLink!(item);
                  return;
                }
              },
              child: Container(
                margin: const EdgeInsets.only(right: 12, bottom: 8),
                child: Text(
                  TIM_t("邀请"),
                  style: const TextStyle(color: Colors.lightGreen),
                ),
              ),
            ),
          ]
        ],
      ),
    );
  }

  Widget generateTopItem(memberInfo) {
    final isDesktopScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
    if (widget.topListItemBuilder != null) {
      final customWidget = widget.topListItemBuilder!(memberInfo);
      if (customWidget != null) {
        return customWidget;
      }
    }
    return InkWell(
        onTap: () {
          if (memberInfo.onRedBaoTap != null) {
            memberInfo.onRedBaoTap!();
          }
        },
        child: Container(
          padding: const EdgeInsets.only(top: 8, left: 16),
          decoration: BoxDecoration(
              border: Border(bottom: BorderSide(color: hexToColor("DBDBDB")))),
          child: Row(
            children: [
              Container(
                height: isDesktopScreen ? 30 : 40,
                width: isDesktopScreen ? 30 : 40,
                margin: const EdgeInsets.only(right: 12, bottom: 12),
                child: memberInfo.icon,
              ),
              Expanded(
                  child: Container(
                padding: const EdgeInsets.only(top: 10, bottom: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      memberInfo.name,
                      style: TextStyle(
                          color: hexToColor("111111"),
                          fontSize: isDesktopScreen ? 14 : 18),
                    ),
                    Expanded(child: Container()),
                    // if (item.id == "newContact")
                    //   const TIMUIKitUnreadCount(),
                    Container(
                      width: 6,
                      height: 10,
                      padding: const EdgeInsets.only(top: 2),
                      child: SvgPicture.asset(
                        'assets/images/svg/my/right.svg',
                      ),
                    )
                  ],
                ),
              ))
            ],
          ),
        ));
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;

    final showList = _getShowList(widget.contactList);
    final isDesktopScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;

    if (widget.topList != null && widget.topList!.isNotEmpty) {
      final topList = widget.topList!
          .map((e) => ISuspensionBeanImpl(memberInfo: e, tagIndex: '@'))
          .toList();
      showList.insertAll(0, topList);
    }

    if (widget.contactList.isEmpty) {
      return Column(
        children: [
          ...showList.map((e) => generateTopItem(e.memberInfo)).toList(),
          Expanded(
              child: widget.emptyBuilder != null
                  ? widget.emptyBuilder!(context)
                  : Container())
        ],
      );
    }

    return AZListViewContainer(
      memberList: showList,
      itemBuilder: (context, index) {
        final memberInfo = showList[index].memberInfo;
        if (memberInfo is TopListItem) {
          return generateTopItem(memberInfo);
        } else {
          // 获取当前项的首字母标签
          final currentTag = showList[index].tagIndex;

          // 找到同一分组内的所有项目
          final sameTagItems = showList
              .where((item) =>
                  item.tagIndex == currentTag &&
                  item.memberInfo is V2TimFriendInfo)
              .toList();

          // 计算当前项在同一分组内的位置
          final indexInGroup =
              sameTagItems.indexWhere((item) => item.memberInfo == memberInfo);
          final groupSize = sameTagItems.length;

          final isFirst = indexInGroup == 0;
          final isLast = indexInGroup == groupSize - 1;
          final isSingle = groupSize == 1;

          return InkWell(
            onTap: () {
              if (widget.isCanSelectMemberItem) {
                if (selectedMember.any((e) => e.userID == memberInfo.userID)) {
                  selectedMember.remove(memberInfo);
                } else {
                  if (selectedMemberIsOverFlow()) {
                    selectedMember = [memberInfo];
                    setState(() {});
                    return;
                  }
                  selectedMember.add(memberInfo);
                }
                if (widget.onSelectedMemberItemChange != null) {
                  widget.onSelectedMemberItemChange!(selectedMember);
                }
                setState(() {});
                return;
              }
              final String showName = _getShowName(memberInfo);
              bool b = showName.startsWith("& ");
              if (widget.onTapItem != null && !b) {
                widget.onTapItem!(memberInfo);
              }
            },
            child: _buildItem(theme, memberInfo,
                isFirst: isFirst, isLast: isLast, isSingle: isSingle),
          );
        }
      },
    );
  }
}

class TopListItem {
  final String name;
  final String? des;
  final String id;
  final Widget? icon;
  final Function()? onTap;

  TopListItem(
      {required this.name, this.des, required this.id, this.icon, this.onTap});
}
