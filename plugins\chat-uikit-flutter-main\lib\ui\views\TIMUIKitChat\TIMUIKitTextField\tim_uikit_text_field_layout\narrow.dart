import 'dart:async';
import 'dart:math';

import 'package:better_player_plus/better_player_plus.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:fc_native_video_thumbnail/fc_native_video_thumbnail.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_callback.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_chat_separate_view_model.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_chat_global_model.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_setting_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme_view_model.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/logger.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/optimize_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/permission.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKitTextField/special_text/DefaultSpecialTextSpanBuilder.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKitTextField/special_text/emoji_text.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKitTextField/tim_uikit_send_sound_message.dart';
import 'package:tencent_keyboard_visibility/tencent_keyboard_visibility.dart';
import 'package:path/path.dart' as p;

GlobalKey<_TIMUIKitTextFieldLayoutNarrowState> narrowTextFieldKey = GlobalKey();

class TIMUIKitTextFieldLayoutNarrow extends StatefulWidget {
  /// sticker panel customization
  final CustomStickerPanel? customStickerPanel;

  final VoidCallback onEmojiSubmitted;
  final Function(int, String) onCustomEmojiFaceSubmitted;
  final Function(String, bool) handleSendEditStatus;
  final VoidCallback backSpaceText;
  final ValueChanged<String> addStickerToText;

  final ValueChanged<String> handleAtText;

  /// Whether to use the default emoji
  final bool isUseDefaultEmoji;

  final bool isUseTencentCloudChatPackageOldKeys;

  final TUIChatSeparateViewModel model;

  /// background color
  final Color? backgroundColor;

  /// control input field behavior
  final TIMUIKitInputTextFieldController? controller;

  /// config for more panel
  final MorePanelConfig? morePanelConfig;

  final String languageType;

  final TextEditingController textEditingController;

  /// conversation id
  final String conversationID;

  /// conversation type
  final ConvType conversationType;

  final FocusNode focusNode;

  /// show more panel
  final bool showMorePanel;

  /// hint text for textField widget
  final String? hintText;

  final int? currentCursor;

  final ValueChanged<int?> setCurrentCursor;

  final VoidCallback onCursorChange;

  /// show send audio icon
  final bool showSendAudio;

  final VoidCallback handleSoftKeyBoardDelete;

  /// on text changed
  final void Function(String)? onChanged;

  final V2TimMessage? repliedMessage;

  final void Function(String)? onDeleteText;

  /// show send emoji icon
  final bool showSendEmoji;

  final VoidCallback onSubmitted;

  final VoidCallback goDownBottom;

  final List<CustomEmojiFaceData> customEmojiStickerList;

  final List<CustomStickerPackage> stickerPackageList;

  const TIMUIKitTextFieldLayoutNarrow(
      {Key? key,
      this.customStickerPanel,
      required this.onEmojiSubmitted,
      required this.onCustomEmojiFaceSubmitted,
      required this.backSpaceText,
      required this.addStickerToText,
      required this.isUseDefaultEmoji,
      this.isUseTencentCloudChatPackageOldKeys = false,
      required this.languageType,
      required this.textEditingController,
      this.morePanelConfig,
      required this.conversationID,
      required this.conversationType,
      required this.focusNode,
      this.currentCursor,
      required this.setCurrentCursor,
      required this.onCursorChange,
      required this.model,
      this.backgroundColor,
      this.onChanged,
      this.onDeleteText,
      required this.handleSendEditStatus,
      required this.handleAtText,
      required this.handleSoftKeyBoardDelete,
      this.repliedMessage,
      required this.onSubmitted,
      required this.goDownBottom,
      required this.showSendAudio,
      required this.showSendEmoji,
      required this.showMorePanel,
      this.hintText,
      required this.customEmojiStickerList,
      this.controller,
      required this.stickerPackageList})
      : super(key: key);

  @override
  State<TIMUIKitTextFieldLayoutNarrow> createState() =>
      _TIMUIKitTextFieldLayoutNarrowState();
}

class _TIMUIKitTextFieldLayoutNarrowState
    extends TIMUIKitState<TIMUIKitTextFieldLayoutNarrow> {
  final TUISettingModel settingModel = serviceLocator<TUISettingModel>();
  late BetterPlayerController _betterPlayerController;

  bool showMore = false;
  bool showSendSoundText = false;
  bool showEmojiPanel = false;
  bool showKeyboard = false;
  Function? setKeyboardHeight;
  double? bottomPadding;

  ValueNotifier<bool> showMoreButton = ValueNotifier(true);

  @override
  void initState() {
    super.initState();
    if (widget.controller != null) {
      widget.controller?.addListener(
        () {
          final actionType = widget.controller?.actionType;
          if (actionType == ActionType.hideAllPanel) {
            hideAllPanel();
          }
        },
      );
    }

    _betterPlayerController =
        BetterPlayerController(const BetterPlayerConfiguration());
  }

  void setSendButton() {
    final value = widget.textEditingController.text;
    // if (isWebDevice() || isAndroidDevice()) {
      if (value.isEmpty && showMoreButton.value != true) {
        showMoreButton.value = true;
      } else if (value.isNotEmpty && showMoreButton.value == true) {
        showMoreButton.value = false;
      }
    // }
  }

  hideAllPanel() {
    widget.focusNode.unfocus();
    widget.currentCursor == null;
    if (showKeyboard != false || showMore != false || showEmojiPanel != false) {
      setState(() {
        showKeyboard = false;
        showMore = false;
        showEmojiPanel = false;
      });
    }
  }

  Widget _getBottomContainer(TUITheme theme) {
    if (showEmojiPanel) {
      return widget.customStickerPanel != null
          ? widget.customStickerPanel!(
              sendTextMessage: () {
                widget.onEmojiSubmitted();
                setSendButton();
              },
              sendFaceMessage: widget.onCustomEmojiFaceSubmitted,
              deleteText: () {
                widget.backSpaceText();
                setSendButton();
              },
              addText: (int unicode) {
                final newText = String.fromCharCode(unicode);
                widget.addStickerToText(newText);
                setSendButton();
                // handleSetDraftText();
              },
              addCustomEmojiText: ((String singleEmojiName) {
                String? emojiName = singleEmojiName.split('.png')[0];
                String compatibleEmojiName = emojiName;
                if (widget.isUseTencentCloudChatPackageOldKeys) {
                  compatibleEmojiName =
                      EmojiUtil.getCompatibleEmojiName(emojiName);
                }

                String newText = '[$compatibleEmojiName]';
                widget.addStickerToText(newText);
                setSendButton();
              }),
              defaultCustomEmojiStickerList: widget.isUseDefaultEmoji
                  ? TUIKitStickerConstData.emojiList
                  : [])
          : StickerPanel(
              isWideScreen: false,
              backgroundColor: Colors.transparent,
              sendTextMsg: () {
                widget.onEmojiSubmitted();
                setSendButton();
              },
              sendFaceMsg: widget.onCustomEmojiFaceSubmitted,
              deleteText: () {
                widget.backSpaceText();
                setSendButton();
              },
              addText: (int unicode) {
                final newText = String.fromCharCode(unicode);
                widget.addStickerToText(newText);
                setSendButton();
                // handleSetDraftText();
              },
              addCustomEmojiText: ((String singleEmojiName) {
                String? emojiName = singleEmojiName.split('.png')[0];
                String compatibleEmojiName = emojiName;
                if (widget.isUseTencentCloudChatPackageOldKeys) {
                  compatibleEmojiName =
                      EmojiUtil.getCompatibleEmojiName(emojiName);
                }

                String newText = '[$compatibleEmojiName]';
                widget.addStickerToText(newText);
                setSendButton();
              }),
              customStickerPackageList: widget.stickerPackageList,
              lightPrimaryColor: theme.lightPrimaryColor);
    }

    if (showMore) {
      return MorePanel(
          morePanelConfig: widget.morePanelConfig,
          conversationID: widget.conversationID,
          conversationType: widget.conversationType);
    }

    return const SizedBox(height: 0);
  }

  double _getBottomHeight() {
    if (showKeyboard) {
      final currentKeyboardHeight = MediaQuery.of(context).viewInsets.bottom;
      double originHeight = settingModel.keyboardHeight;
      if (currentKeyboardHeight != 0) {
        if (currentKeyboardHeight >= originHeight) {
          originHeight = currentKeyboardHeight;
        }
        if (setKeyboardHeight != null) {
          setKeyboardHeight!(currentKeyboardHeight);
        }
      }
      final height = originHeight != 0 ? originHeight : currentKeyboardHeight;
      return height;
    } else if (showMore || showEmojiPanel) {
      return 248.0 + (bottomPadding ?? 0.0);
    } else if (widget.textEditingController.text.length >= 46 &&
        showKeyboard == false) {
      return 25 + (bottomPadding ?? 0.0);
    } else {
      return bottomPadding ?? 0;
    }
  }

  _openMore() {
    if (!showMore) {
      widget.focusNode.unfocus();
      widget.setCurrentCursor(null);
    }
    setState(() {
      showKeyboard = false;
      showEmojiPanel = false;
      showSendSoundText = false;
      showMore = !showMore;
    });
  }

  _openEmojiPanel() {
    widget.onCursorChange();
    showKeyboard = showEmojiPanel;
    if (showEmojiPanel) {
      widget.focusNode.requestFocus();
    } else {
      widget.focusNode.unfocus();
    }

    setState(() {
      showMore = false;
      showSendSoundText = false;
      showEmojiPanel = !showEmojiPanel;
    });
  }

  _debounce(
    Function(String text) fun, [
    Duration delay = const Duration(milliseconds: 30),
  ]) {
    Timer? timer;
    return (String text) {
      if (timer != null) {
        timer?.cancel();
      }

      timer = Timer(delay, () {
        fun(text);
      });
    };
  }

  String getAbstractMessage(V2TimMessage message) {
    final String? customAbstractMessage =
        widget.model.abstractMessageBuilder != null
            ? widget.model.abstractMessageBuilder!(message)
            : null;
    return customAbstractMessage ??
        MessageUtils.getAbstractMessageAsync(
            message, widget.model.groupMemberList ?? []);
  }

  _buildRepliedMessage(V2TimMessage? repliedMessage) {
    final haveRepliedMessage = repliedMessage != null;
    if (haveRepliedMessage) {
      final String text = "${MessageUtils.getDisplayName(repliedMessage)}";
      final String subText = getAbstractMessage(repliedMessage);
      return Container(
        color: Color(0xFFF4F0EB),
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  Container(
                    color: hexToColor('7B654D'),
                    height: 44,
                    width: 4,
                  ),
                  const SizedBox(
                    width: 7,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        text,
                        softWrap: true,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: hexToColor("7B654D"), fontSize: 12),
                      ),
                      Text(
                        subText,
                        softWrap: true,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: hexToColor("000000"), fontSize: 11),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(
              width: 16,
            ),
            InkWell(
              onTap: () {
                widget.model.repliedMessage = null;
              },
              child: Image.asset(
                'images/ic_close.png',
                package: 'tencent_cloud_chat_uikit',
                width: 20,
                height: 20,
              ),

              // Icon(Icons.clear, color: hexToColor("8f959e"), size: 18),
            )
          ],
        ),
      );
    }
    return Container();
  }

  _sendImageFromCamera(TUIChatSeparateViewModel model, TUITheme theme,
      {required isVideo}) async {
    try {
      if (!await Permissions.checkPermission(
        context,
        Permission.camera.value,
        theme,
      )) {
        return;
      }
      await Permissions.checkPermission(
        context,
        Permission.microphone.value,
        theme,
      );

      final convID = widget.conversationID;
      final convType = widget.conversationType;
      final ImagePicker picker = ImagePicker();
      XFile? originFile;
      if (isVideo) {
        originFile = await picker.pickVideo(source: ImageSource.camera);
      } else {
        originFile = await picker.pickImage(source: ImageSource.camera);
      }
      final size = await originFile!.length();
      if (!isVideo) {
        if (size >= MorePanelConfig.IMAGE_MAX_SIZE) {
          onTIMCallback(TIMCallback(
              type: TIMCallbackType.INFO,
              infoRecommendText: TIM_t("文件大小超出了限制")));
          return;
        }

        MessageUtils.handleMessageError(
            model.sendImageMessage(
                imagePath: originFile.path, convID: convID, convType: convType),
            context);
      } else {
        // 监听视频准备完成事件
        _betterPlayerController.addEventsListener((event) {
          if (event.betterPlayerEventType ==
              BetterPlayerEventType.initialized) {
            // 获取视频时长（单位：秒）
            int durationInSeconds = _betterPlayerController
                .videoPlayerController?.value.duration?.inSeconds ??
                0;
            _sendVideoMessage(originFile!.path, durationInSeconds, size, model);
          }
        });

        // 加载视频源
        _betterPlayerController.setupDataSource(
          BetterPlayerDataSource(
            BetterPlayerDataSourceType.file,
            originFile.path, // 替换为你的视频 URL
          ),
        );
      }
    } catch (error) {
      outputLogger.i("err: $error");
    }
  }

  _sendVideoMessage(String originFilePath, int duration, int size,
      TUIChatSeparateViewModel model) async {
    if (size >= MorePanelConfig.VIDEO_MAX_SIZE) {
      onTIMCallback(TIMCallback(
          type: TIMCallbackType.INFO, infoRecommendText: TIM_t("文件大小超出了限制")));
      return;
    }

    final plugin = FcNativeVideoThumbnail();

    final convID = widget.conversationID;
    final convType = widget.conversationType;

    String tempPath = (await getTemporaryDirectory()).path +
        p.basename(originFilePath) +
        ".jpeg";

    await plugin.getVideoThumbnail(
      srcFile: originFilePath,
      destFile: tempPath,
      format: 'jpeg',
      width: 1280,
      quality: 100,
      height: 1280,
    );
    MessageUtils.handleMessageError(
        model.sendVideoMessage(
            videoPath: originFilePath,
            duration: duration,
            snapshotPath: tempPath,
            convID: convID,
            convType: convType),
        context);
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final theme = value.theme;

    setKeyboardHeight ??= OptimizeUtils.debounce((height) {
      settingModel.keyboardHeight = height;
    }, const Duration(seconds: 1));

    final debounceFunc = _debounce((value) {
      // if (isWebDevice() || isAndroidDevice()) {
        if (value.isEmpty && showMoreButton.value != true) {
          showMoreButton.value = true;
        } else if (value.isNotEmpty && showMoreButton.value == true) {
          showMoreButton.value = false;
        }
      // }
      if (widget.onChanged != null) {
        widget.onChanged!(value);
      }
      widget.handleAtText(value);
      widget.handleSendEditStatus(value, true);
      final isEmpty = value.isEmpty;
      if (isEmpty) {
        widget.handleSoftKeyBoardDelete();
      }
    }, const Duration(milliseconds: 80));

    final MediaQueryData data = MediaQuery.of(context);
    EdgeInsets padding = data.padding;
    if (bottomPadding == null || padding.bottom > bottomPadding!) {
      bottomPadding = padding.bottom;
    }

    return GestureDetector(
      onTap: () {},
      child: Column(
        children: [
          _buildRepliedMessage(widget.repliedMessage),
          Selector<TUIThemeViewModel,TUITheme>(
            selector: (bc,tv) => tv.theme,
            builder: (ctx,ttheme,w) {
              return Container(
                color: ttheme.chatHeaderBgColor ?? Color(0xFFF4F0EB),
                child: Column(
                  children: [
                    Container(
                      padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                      constraints: const BoxConstraints(minHeight: 50),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (widget.showMorePanel)
                            ValueListenableBuilder<bool>(
                              valueListenable: showMoreButton,
                              builder: (context, value, _) {
                                return Offstage(
                                  offstage: !value,
                                  child: InkWell(
                                    onTap: () {
                                      _openMore();
                                      widget.goDownBottom();
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.only(right: 12),
                                      child: PlatformUtils().isWeb
                                          ? Icon(Icons.add,
                                          color: Colors.black, size: 32)
                                          : SvgPicture.asset(
                                        'images/add.svg',
                                        package: 'tencent_cloud_chat_uikit',
                                        color: const Color.fromRGBO(68, 68, 68, 1),
                                        height: 19,
                                        width: 19,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),

                          Expanded(
                            child: showSendSoundText
                                ? SendSoundMessage(
                                onDownBottom: widget.goDownBottom,
                                conversationID: widget.conversationID,
                                conversationType: widget.conversationType)
                                : RawKeyboardListener(
                              autofocus: true,
                              focusNode: FocusNode(),
                              onKey: (key) {
                                if (key is RawKeyDownEvent &&
                                    key.logicalKey ==
                                        LogicalKeyboardKey.backspace) {
                                  if (widget.onDeleteText != null) {
                                    widget.onDeleteText!(
                                        widget.textEditingController.text);
                                  }
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16),
                                    color: Colors.white
                                ),
                                padding: EdgeInsets.only(right: 12),
                                child: Row(children: [
                                  Expanded(child: KeyboardVisibility(
                                      child: ExtendedTextField(
                                          maxLines: 4, minLines: 1,
                                          focusNode: widget.focusNode,
                                          onChanged: debounceFunc,
                                          onTap: () {
                                            showKeyboard = true;
                                            widget.goDownBottom();
                                            setState(() {
                                              showEmojiPanel = false;
                                              showMore = false;
                                            });
                                          },
                                          keyboardType: TextInputType.multiline,
                                          textInputAction:
                                          PlatformUtils().isAndroid
                                              ? TextInputAction.newline
                                              : TextInputAction.send,
                                          onEditingComplete: () {
                                            widget.onSubmitted();
                                            if (showKeyboard) {
                                              widget.focusNode.requestFocus();
                                            }
                                            setState(() {
                                              if (widget.textEditingController
                                                  .text.isEmpty) {
                                                showMoreButton.value = true;
                                              }
                                            });
                                          },
                                          textAlignVertical:
                                          TextAlignVertical.top,
                                          decoration: InputDecoration(
                                              border: InputBorder.none,
                                              hintStyle: const TextStyle(
                                                // fontSize: 10,
                                                color: Color(0xffAEA4A3),
                                              ),
                                              fillColor: Colors.transparent,
                                              filled: true,
                                              isDense: true,
                                              hintText: widget.hintText ?? ''),
                                          controller:
                                          widget.textEditingController,
                                          specialTextSpanBuilder: PlatformUtils()
                                              .isWeb
                                              ? null
                                              : DefaultSpecialTextSpanBuilder(
                                            isUseQQPackage: widget
                                                .model
                                                .chatConfig
                                                .stickerPanelConfig
                                                ?.useQQStickerPackage ??
                                                true,
                                            isUseTencentCloudChatPackage:
                                            widget
                                                .model
                                                .chatConfig
                                                .stickerPanelConfig
                                                ?.useTencentCloudChatStickerPackage ??
                                                true,
                                            isUseTencentCloudChatPackageOldKeys:
                                            widget
                                                .model
                                                .chatConfig
                                                .stickerPanelConfig
                                                ?.useTencentCloudChatStickerPackageOldKeys ??
                                                false,
                                            customEmojiStickerList: widget
                                                .customEmojiStickerList,
                                            showAtBackground: true,
                                            checkHttpLink: false,
                                          )),
                                      onChanged: (bool visibility) {
                                        if (showKeyboard != visibility) {
                                          setState(() {
                                            showKeyboard = visibility;
                                          });
                                        }
                                      })),
                                  if (widget.showSendEmoji)
                                    InkWell(
                                      onTap: () {
                                        _openEmojiPanel();
                                        widget.goDownBottom();
                                      },
                                      child: PlatformUtils().isWeb
                                          ? Icon(
                                          showEmojiPanel
                                              ? Icons.keyboard_alt_outlined
                                              : Icons.mood_outlined,
                                          color: hexToColor("5c6168"),
                                          size: 32)
                                          : SvgPicture.asset(
                                        showEmojiPanel
                                            ? 'images/keyboard.svg'
                                            : 'images/wenjian.svg',
                                        package: 'tencent_cloud_chat_uikit',
                                        color: const Color.fromRGBO(68, 68, 68, 1),
                                        height: showEmojiPanel?14:19,
                                        width: 19,
                                      ),
                                    ),
                                ],),
                              ),
                            ),
                          ),
                          if (PlatformUtils().isMobile && widget.showSendAudio)
                            ValueListenableBuilder<bool>(
                              valueListenable: showMoreButton,
                              builder: (ctx,v,w) {
                                return Offstage(
                                  offstage: !v,
                                  child: Padding(
                                    padding: EdgeInsets.only(left: 12),
                                    child: InkWell(
                                      onTap: () {
                                        _sendImageFromCamera(widget.model, theme, isVideo: false);
                                      },
                                      child: SvgPicture.asset(
                                        'images/zhaoxiangji.svg',
                                        package: 'tencent_cloud_chat_uikit',
                                        color: const Color.fromRGBO(68, 68, 68, 1),
                                        height: showEmojiPanel?14:19,
                                        width: 19,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          if (PlatformUtils().isMobile && widget.showSendAudio)
                            ValueListenableBuilder<bool>(
                              valueListenable: showMoreButton,
                              builder: (ctx,v,w) {
                                return Offstage(
                                  offstage: !v,
                                  child: InkWell(
                                    onTap: () async {
                                      showKeyboard = showSendSoundText;
                                      if (showSendSoundText) {
                                        widget.focusNode.requestFocus();
                                      }
                                      if (await Permissions.checkPermission(
                                        context,
                                        Permission.microphone.value,
                                        theme,
                                      )) {
                                        setState(() {
                                          showEmojiPanel = false;
                                          showMore = false;
                                          showSendSoundText = !showSendSoundText;
                                        });
                                      }
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.only(left: 12),
                                      child: SvgPicture.asset(
                                        showSendSoundText
                                            ? 'images/keyboard.svg'
                                            : 'images/voice.svg',
                                        package: 'tencent_cloud_chat_uikit',
                                        color: const Color.fromRGBO(68, 68, 68, 1),
                                        height: showSendSoundText ? 14 : 19,
                                        width: showSendSoundText ? 19 : 13,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          // if (isAndroidDevice() || isWebDevice())
                            ValueListenableBuilder<bool>(
                              valueListenable: showMoreButton,
                              builder: (ctx,v,w) {
                                return Offstage(
                                  offstage: v,
                                  child: InkWell(
                                    onTap: () {
                                      widget.onSubmitted();
                                      if (showKeyboard) {
                                        widget.focusNode.requestFocus();
                                      }
                                      if (widget.textEditingController.text.isEmpty) {
                                        showMoreButton.value = true;
                                      }
                                    },
                                    child: Padding(padding: EdgeInsets.only(left: 12),
                                      child: SvgPicture.asset(
                                        'images/svg/send.svg',
                                        package: 'tencent_cloud_chat_uikit',
                                        height: 28,
                                        width: 28,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          // SizedBox(
                          //   height: 32.0,
                          //   child: ElevatedButton(
                          //     onPressed: () {
                          //       widget.onSubmitted();
                          //       if (showKeyboard) {
                          //         widget.focusNode.requestFocus();
                          //       }
                          //       if (widget.textEditingController.text.isEmpty) {
                          //         setState(() {
                          //           showMoreButton = true;
                          //         });
                          //       }
                          //     },
                          //     child: Text(TIM_t("发送222")),
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                    AnimatedContainer(
                      duration: Duration(
                          milliseconds: (showKeyboard && PlatformUtils().isAndroid)
                              ? 200
                              : 340),
                      curve: Curves.fastOutSlowIn,
                      height: max(_getBottomHeight(), 0.0),
                      child: _getBottomContainer(theme),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
