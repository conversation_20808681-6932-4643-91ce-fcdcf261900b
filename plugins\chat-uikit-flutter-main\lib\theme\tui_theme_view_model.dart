import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';


class ChatThemeGroup {
  final String bgImg;
  final Color headerColor;
  final Color msgBgColor;
  final Color msgTextColor;
  ChatThemeGroup({required this.bgImg, required this.headerColor, required this.msgBgColor, required this.msgTextColor});
}

class TUIThemeViewModel extends ChangeNotifier {
  TUITheme _theme = CommonColor.defaultTheme;

  TUITheme get theme {
    return _theme;
  }

  set theme(TUITheme theme) {
    _theme = theme;
    notifyListeners();
  }
}
extension TUIThemeViewModelUpdate on TUIThemeViewModel {

  /// 更新聊天背景图
  void updateChatBgImg(ChatThemeGroup ctg) {
    _theme.conversationCurrentImgPath = ctg.bgImg;
    _theme.chatMessageItemFromSelfBgColor = ctg.msgBgColor;
    _theme.chatMessageItemTextColor = ctg.msgTextColor;
    _theme.chatHeaderBgColor = ctg.headerColor;
    theme = TUITheme.fromJson(_theme.toJson());
  }

}