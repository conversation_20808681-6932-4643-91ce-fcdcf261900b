import 'package:azlistview_all_platforms/azlistview_all_platforms.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme_view_model.dart';

class AZListViewContainer extends StatefulWidget {
  final List<ISuspensionBeanImpl>? memberList;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final Widget Function(BuildContext context, int index)? susItemBuilder;
  final bool isShowIndexBar;

  const AZListViewContainer(
      {Key? key,
      required this.memberList,
      required this.itemBuilder,
      this.isShowIndexBar = true,
      this.susItemBuilder})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _AZListViewContainerState();
}

class _AZListViewContainerState extends TIMUIKitState<AZListViewContainer> {
  List<ISuspensionBeanImpl>? _list;

  addShowSuspension(List<ISuspensionBeanImpl> curList) {
    for (int i = 0; i < curList.length; i++) {
      final current = curList[i];
      final prev = i > 0 ? curList[i - 1] : null;
      final next = i < curList.length - 1 ? curList[i + 1] : null;

      current.isShowSuspension = i == 0 || current.tagIndex != prev?.tagIndex;
      current.isFirstOfGroup = current.isShowSuspension;
      current.isLastOfGroup =
          i == curList.length - 1 || current.tagIndex != next?.tagIndex;
    }
    return curList;
  }

  static Widget getSusItem(BuildContext context, String tag,
      {double susHeight = 37}) {
    final theme = Provider.of<TUIThemeViewModel>(context).theme;
    return Container(
      height: 30,
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.only(left: 33.0),
      alignment: Alignment.centerLeft,
      child: Text(
        tag.replaceAll("&", "${TIM_t('邀请加入')} Funshot"),
        softWrap: true,
        style: TextStyle(
          fontSize: 10.0,
          color: theme.weakTextColor,
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    setState(() {
      _list = addShowSuspension(widget.memberList!);
    });
  }

  @override
  void didUpdateWidget(covariant AZListViewContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    setState(() {
      _list = addShowSuspension(widget.memberList!);
    });
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final isDesktopScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
    return ChangeNotifierProvider.value(
        value: serviceLocator<TUIThemeViewModel>(),
        child: Consumer<TUIThemeViewModel>(
            builder: (context, tuiTheme, child) => AzListView(
                physics: const BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics()),
                data: _list!,
                itemCount: _list!.length,
                itemBuilder: widget.itemBuilder,
                indexBarData: (!isDesktopScreen && widget.isShowIndexBar)
                    ? SuspensionUtil.getTagIndexList(_list!)
                        .where((element) => element != "@")
                        .toList()
                    : [],
                indexBarWidth: 20,
                indexBarOptions: const IndexBarOptions(
                    textStyle:
                        TextStyle(color: Color(0xFF6A6C6C), fontSize: 10)),
                susItemBuilder: (BuildContext context, int index) {
                  if (widget.susItemBuilder != null) {
                    return widget.susItemBuilder!(context, index);
                  }
                  ISuspensionBeanImpl model = _list![index];
                  if (model.getSuspensionTag() == "@") {
                    return Container();
                  }
                  return getSusItem(context, model.getSuspensionTag());
                })));
  }
}

class ISuspensionBeanImpl<T> extends ISuspensionBean {
  String tagIndex;
  T memberInfo;
  bool isFirstOfGroup = false;
  bool isLastOfGroup = false;

  ISuspensionBeanImpl({required this.tagIndex, required this.memberInfo});

  @override
  String getSuspensionTag() => tagIndex;
}
