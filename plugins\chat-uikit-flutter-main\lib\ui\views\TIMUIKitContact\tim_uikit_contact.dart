import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_friend_info.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/life_cycle/friend_list_life_cycle.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_friendship_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

export 'package:tencent_cloud_chat_uikit/ui/widgets/contact_list.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';

class TIMUIKitContact extends StatefulWidget {
  /// the callback after clicking contact item
  final void Function(V2TimFriendInfo item)? onTapItem;
  final void Function(V2TimFriendInfo item)? onTapJoinLink;

  /// the list on top
  final List<TopListItem>? topList;

  /// the builder for the list on top
  final Widget? Function(TopListItem item)? topListItemBuilder;

  /// The widget shows when no contacts exists.
  final Widget Function(BuildContext context)? emptyBuilder;

  /// the life cycle hooks for friend list or contacts list business logic
  final FriendListLifeCycle? lifeCycle;

  /// Control if shows the online status for each user on its avatar.
  final bool isShowOnlineStatus;

  // 本机联系人数据
  final List<V2TimFriendInfo> localContacts;

  final String? keyword;

  const TIMUIKitContact({
    Key? key,
    this.onTapItem,
    this.onTapJoinLink,
    this.lifeCycle,
    this.topList,
    this.topListItemBuilder,
    this.emptyBuilder,
    this.keyword,
    this.isShowOnlineStatus = true,
    this.localContacts = const [],
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _TIMUIKitContactState();
}

class _TIMUIKitContactState extends TIMUIKitState<TIMUIKitContact> {
  final TUIFriendShipViewModel model = serviceLocator<TUIFriendShipViewModel>();
  String currentItem = "";

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant TIMUIKitContact oldWidget) {
    if (oldWidget.localContacts.length != widget.localContacts.length)
      setTIMState(() {});
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final theme = value.theme;
    final isDesktopScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
    return MultiProvider(
        providers: [
          ChangeNotifierProvider.value(value: model),
        ],
        builder: (context, w) {
          final model = Provider.of<TUIFriendShipViewModel>(context);
          model.contactListLifeCycle = widget.lifeCycle;
          print("memberList: ${model.friendList}");

          var blockList = model.blockList;
          var filteredMemberList = (model.friendList ?? [])
              .where((member) =>
                  !blockList.any((blocked) => blocked.userID == member.userID))
              .toList();
          var memberList = filteredMemberList + widget.localContacts;


          if (widget.keyword != null) {
            memberList = memberList.where((element) {
              return (element.friendRemark?.contains(widget.keyword ?? '') ??
                      false) ||
                  (element.userProfile?.nickName
                          ?.contains(widget.keyword ?? '') ??
                      false) ||
                  (element.userProfile?.userID
                          ?.contains(widget.keyword ?? '') ??
                      false);
            }).toList();
          }
          return ContactList(
            currentItem: currentItem,
            emptyBuilder: widget.emptyBuilder,
            isShowOnlineStatus: widget.isShowOnlineStatus,
            contactList: memberList,
            onTapItem: (item) {
              if (isDesktopScreen) {
                setState(() {
                  currentItem = item.userID;
                });
              }
              if (widget.onTapItem != null) {
                widget.onTapItem!(item);
              }
            },
            topList: widget.topList,
            topListItemBuilder: widget.topListItemBuilder,
            onTapJoinLink: widget.onTapJoinLink,
          );
        });
  }
}
