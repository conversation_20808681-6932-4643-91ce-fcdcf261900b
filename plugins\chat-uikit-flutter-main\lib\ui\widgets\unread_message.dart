/*
 * @Author: your name
 * @Date: 2025-06-18 09:35:58
 * @LastEditTime: 2025-06-19 08:52:14
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /flt_ship/plugins/chat-uikit-flutter-main/lib/ui/widgets/unread_message.dart
 */
import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';

class UnreadMessage extends TIMUIKitStatelessWidget {
  final int unreadCount;
  final double? width;
  final double? height;
  final Color? backgroundColor;

  UnreadMessage(
      {Key? key,
      required this.unreadCount,
      this.backgroundColor,
      this.width = 22.0,
      this.height = 22.0})
      : super(key: key);

  String generateUnreadText() =>
      unreadCount > 99 ? '99+' : unreadCount.toString();

  double generateFontSize(String text) => text.length * -2 + 12;

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;

    final unreadText = generateUnreadText();
    final fontSize = generateFontSize(unreadText);
    return Container(
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ??
            theme.conversationItemUnreadCountBgColor ??
            CommonColor.cautionColor,
      ),
      child: unreadText != "0"
          ? Center(
              child: Text(
                unreadText,
                style: TextStyle(
                  color: theme.conversationItemUnreadCountTextColor,
                  fontSize: fontSize,
                ),
              ),
            )
          : null,
    );
  }
}
