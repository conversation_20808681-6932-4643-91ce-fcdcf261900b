import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/enum/friend_type_enum.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info_result.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_friendship_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/tim_uikit_wide_modal_operation_key.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_input.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/wide_popup.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/life_cycle/add_friend_life_cycle.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_self_info_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/core_services_implements.dart';
import 'package:tencent_cloud_chat_uikit/data_services/friendShip/friendship_services.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitAddFriend/tim_uikit_send_application.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/avatar.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';

class UserSearchResult {
  final V2TimUserFullInfo userInfo;
  final bool isFriend;

  UserSearchResult({required this.userInfo, required this.isFriend});
}

class TIMUIKitAddFriend extends StatefulWidget {
  final bool? isShowDefaultGroup;

  /// You may navigate to user profile page, if friendship relationship exists.
  final Function(String userID) onTapAlreadyFriendsItem;
  final Function(TUISelfInfoViewModel model, V2TimUserFullInfo friendInfo)?
      onSendFriend;

  /// The life cycle hooks for adding friends and contact business logic
  final AddFriendLifeCycle? lifeCycle;

  /// The callback function to close the widget upon completion by the parent component.
  final VoidCallback? closeFunc;

  final Function(V2TimGroupInfoResult res)? joinGroupBack;

  const TIMUIKitAddFriend(
      {Key? key,
      this.isShowDefaultGroup = false,
      this.lifeCycle,
      required this.onTapAlreadyFriendsItem,
      this.onSendFriend,
      this.closeFunc,
      this.joinGroupBack})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _TIMUIKitAddFriendState();
}

class _TIMUIKitAddFriendState extends TIMUIKitState<TIMUIKitAddFriend> {
  final TextEditingController _controller = TextEditingController();
  final CoreServicesImpl _coreServicesImpl = serviceLocator<CoreServicesImpl>();
  final FriendshipServices _friendshipServices =
      serviceLocator<FriendshipServices>();
  final TUISelfInfoViewModel _selfInfoViewModel =
      serviceLocator<TUISelfInfoViewModel>();
  final TUIFriendShipViewModel _friendShipViewModel =
      serviceLocator<TUIFriendShipViewModel>();
  final FocusNode _focusNode = FocusNode();
  bool isFocused = false;
  bool showResult = false;
  List<UserSearchResult>? searchResult;

  late TextEditingController textEditingController = TextEditingController();
  final FocusNode focusNode = FocusNode();
  GlobalKey<dynamic> inputTextField = GlobalKey();

  Widget _searchResultItemBuilder(
      UserSearchResult searchResultItem, TUITheme theme) {
    final isDesktopScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;

    final friendInfo = searchResultItem.userInfo;
    final isFriend = searchResultItem.isFriend;
    final faceUrl = friendInfo.faceUrl ?? "";
    print('-----friendInfo------: ${friendInfo.toJson()}');
    final userID = friendInfo.userID ?? "";
    final String showName =
        ((friendInfo.nickName != null && friendInfo.nickName!.isNotEmpty)
                ? friendInfo.nickName
                : userID) ??
            "";

    return InkWell(
      onTap: () async {
        if (userID == _selfInfoViewModel.loginInfo?.userID) {
          return;
        }
        // 如果已经是好友，直接跳转到好友页面
        if (isFriend) {
          widget.onTapAlreadyFriendsItem(userID);
          return;
        }

        if (userID == _selfInfoViewModel.loginInfo?.userID) {
          widget.onTapAlreadyFriendsItem(userID);
          return;
        }

        if (isDesktopScreen) {
          if (widget.closeFunc != null) {
            widget.closeFunc!();
          }
          TUIKitWidePopup.showPopupWindow(
            operationKey: TUIKitWideModalOperationKey.addFriend,
            context: context,
            width: MediaQuery.of(context).size.width * 0.3,
            height: MediaQuery.of(context).size.width * 0.4,
            title: TIM_t("添加好友"),
            child: (closeFuncSendApplication) => SendApplication(
                lifeCycle: widget.lifeCycle,
                isShowDefaultGroup: widget.isShowDefaultGroup ?? false,
                friendInfo: friendInfo,
                model: _selfInfoViewModel),
          );
        } else {
          if (widget.onSendFriend != null)
            widget.onSendFriend!(_selfInfoViewModel, friendInfo);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(9)),
            color: Colors.white),
        child: Row(
          children: [
            Container(
              width: isDesktopScreen ? 38 : 48,
              height: isDesktopScreen ? 38 : 48,
              margin: const EdgeInsets.only(right: 16),
              child: Avatar(
                borderRadius: BorderRadius.circular(100),
                faceUrl: faceUrl,
                showName: showName,
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    showName,
                    style: TextStyle(
                        color: theme.darkTextColor,
                        fontSize: isDesktopScreen ? 16 : 12),
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  Text(
                    "ID: $userID",
                    style: TextStyle(fontSize: 11, color: theme.weakTextColor),
                  )
                ],
              ),
            ),
            // 根据好友状态显示不同的按钮
            if (isFriend)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: hexToColor('F0F0F0'),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  TIM_t('已添加'),
                  style: TextStyle(fontSize: 10, color: hexToColor('999999')),
                ),
              )
            else
              InkWell(
                onTap: () async {
                  if (userID == _selfInfoViewModel.loginInfo?.userID) {
                    return;
                  }

                  if (isDesktopScreen) {
                    if (widget.closeFunc != null) {
                      widget.closeFunc!();
                    }
                    TUIKitWidePopup.showPopupWindow(
                      operationKey: TUIKitWideModalOperationKey.addFriend,
                      context: context,
                      width: MediaQuery.of(context).size.width * 0.3,
                      height: MediaQuery.of(context).size.width * 0.4,
                      title: TIM_t("添加好友"),
                      child: (closeFuncSendApplication) => SendApplication(
                          lifeCycle: widget.lifeCycle,
                          isShowDefaultGroup:
                              widget.isShowDefaultGroup ?? false,
                          friendInfo: friendInfo,
                          model: _selfInfoViewModel),
                    );
                  } else {
                    // 移动端：使用你的自定义添加好友页面
                    if (widget.onSendFriend != null) {
                      widget.onSendFriend!(_selfInfoViewModel, friendInfo);
                    }
                  }
                },
                child: Text(
                  userID == _selfInfoViewModel.loginInfo?.userID ? TIM_t('自己') : TIM_t('添加'),
                  style: TextStyle(fontSize: 10, color: hexToColor('1DAA61')),
                ),
              )
          ],
        ),
      ),
    );
  }

  List<Widget> _searchResultBuilder(
      List<UserSearchResult>? searchResult, TUITheme theme) {
    final noResult = searchResult == null || searchResult.isEmpty;
    if (noResult) {
      return [
        Container(
          margin: const EdgeInsets.only(top: 20),
          child: Center(
            child: Text(TIM_t("该用户不存在"),
                style: TextStyle(color: theme.weakTextColor, fontSize: 14)),
          ),
        )
      ];
    }
    return searchResult.map((e) => _searchResultItemBuilder(e, theme)).toList();
  }

  // 是不是 搜索 群
  bool _isGroup = false;
  List<V2TimGroupInfoResult> groups = [];

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      final _isFocused = _focusNode.hasFocus;
      isFocused = _isFocused;
      setState(() {});
    });

    // 监听好友列表变化
    _friendShipViewModel.addListener(_onFriendListChanged);
  }

  void _onFriendListChanged() {
    // 当好友列表发生变化时，刷新搜索结果
    if (searchResult != null && searchResult!.isNotEmpty) {
      final userInfoList = searchResult!.map((e) => e.userInfo).toList();
      _updateSearchResults(userInfoList);
    }
  }

  @override
  void dispose() {
    _friendShipViewModel.removeListener(_onFriendListChanged);
    super.dispose();
  }

  searchFriend(String params) async {
    final res = await Future.wait([
      TencentImSDKPlugin.v2TIMManager
          .getGroupManager()
          .getGroupsInfo(groupIDList: [params]),
      _coreServicesImpl.getUsersInfo(userIDList: [params])
    ]);

    if (res[0].code == 0 &&
        res[0].data != null &&
        (res[0].data?.firstOrNull as V2TimGroupInfoResult?)?.resultCode == 0) {
      // 搜索到群
      _isGroup = true;
      showResult = true;
      groups = (res[0].data as List<V2TimGroupInfoResult>?) ?? [];
    } else if (res[1].code == 0 && res[1].data != null) {
      // 搜索到 个人
      _isGroup = false;
      showResult = true;
      await _updateSearchResults(res[1].data!);
    } else {
      showResult = false;
      searchResult = null;
    }
    setState(() {});
  }

  // 更新搜索结果的好友关系状态
  Future<void> _updateSearchResults(List userInfoList) async {
    List<UserSearchResult> results = [];

    for (var userInfo in userInfoList) {
      // 每次都重新检查好友关系，确保状态是最新的
      final isFriend = await _friendShipViewModel.isFriend(userInfo.userID!);
      print("用户 ${userInfo.userID} 是否为好友: $isFriend");

      results.add(UserSearchResult(userInfo: userInfo, isFriend: isFriend));
    }

    setState(() {
      searchResult = results;
    });
  }

  // 刷新搜索结果中的好友状态
  void refreshFriendStatus() async {
    if (searchResult != null && searchResult!.isNotEmpty) {
      List<UserSearchResult> updatedResults = [];

      for (var result in searchResult!) {
        final isFriend =
            await _friendShipViewModel.isFriend(result.userInfo.userID!);
        updatedResults.add(
            UserSearchResult(userInfo: result.userInfo, isFriend: isFriend));
      }

      setState(() {
        searchResult = updatedResults;
      });
    }
  }

  /// 组信息
  List<Widget> _groupBuilder() => [
        ...groups.map((ele) {
          final isAuth =
              (ele.groupInfo?.customInfo?["icon_auth"] ?? "0") == "1";
          return Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(9)),
                color: Colors.white),
            child: Row(
              children: [

                Container(
                  width: 48,
                  height: 48,

                  margin: const EdgeInsets.only(right: 16),
                  child: Avatar(
                    type: 2,
                    borderRadius: BorderRadius.circular(100),
                    faceUrl: ele.groupInfo!.faceUrl ?? '',
                    showName: '',
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                  ele.groupInfo?.groupName ?? "",
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                            fontSize: 12),
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      Text(
                        "${TIM_t('群组')}ID: ${ele.groupInfo?.groupID ?? ""}",
                        style: TextStyle(fontSize: 11, color: Colors.grey),
                      )
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),
        InkWell(
          onTap: () {
            if (widget.joinGroupBack != null) widget.joinGroupBack!(groups[0]);
          },
          child: Container(
            margin: EdgeInsets.symmetric(vertical: 12),
            padding: EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(12)),
                color: Colors.white),
            child: Center(
              child: Text(
                TIM_t("申请加入"),
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12,color: hexToColor('1DAA61'),height: 1.2),
              ),
            ),
          ),
        )
      ];

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: _selfInfoViewModel),
        ChangeNotifierProvider.value(value: _friendShipViewModel),
      ],
      builder: (BuildContext context, Widget? w) {
        // 监听好友关系变化
        context.watch<TUIFriendShipViewModel>();
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
              child: Row(
                children: [
                  Expanded(
                      child: SizedBox(
                    height: 27,
                    child: TextField(
                      style: TextStyle(fontSize: 12),
                      autofocus: true,
                      focusNode: _focusNode,
                      controller: _controller,
                      onChanged: (value) {
                        if (value.trim().isEmpty) {
                          setState(() {
                            showResult = false;
                          });
                        }
                      },
                      textInputAction: TextInputAction.search,
                      onSubmitted: (_) {
                        final searchParams = _controller.text;
                        if (searchParams.trim().isNotEmpty) {
                          searchFriend(searchParams.trim());
                          showResult = true;
                          _focusNode.requestFocus();
                          setState(() {});
                        }
                      },
                      decoration: InputDecoration(
                          prefixIcon: IconButton(
                            icon: Image.asset(
                                'assets/images/png/chat/icon_search.png',
                                width: 12,
                                height: 12,
                                color: Color(0xFF7C7B80)),
                            onPressed: () {},
                          ),
                          suffixIcon: IconButton(
                            icon: Image.asset(
                              'images/close.png',
                              package: 'tencent_cloud_chat_uikit',
                            ),
                            onPressed: () {
                              _controller.text = '';
                            },
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(9),
                            borderSide: const BorderSide(
                              width: 0,
                              style: BorderStyle.none,
                            ),
                          ),
                          contentPadding: EdgeInsets.zero,
                          hintStyle: TextStyle(
                            color: hexToColor('646464'),
                            fontSize: 12,
                          ),
                          labelStyle: TextStyle(
                            color: hexToColor('646464'),
                            fontSize: 12,
                          ),
                          fillColor: hexToColor('ECECEC'),
                          filled: true,
                          hintText:'${TIM_t("搜索用户ID")}/${TIM_t('群组')} ID' ),
                    ),
                  )),
                  const SizedBox(width: 14),
                  InkWell(
                      onTap: () {
                        final searchParams = _controller.text;
                        if (searchParams.trim().isNotEmpty) {
                          searchFriend(searchParams);
                          showResult = true;
                          _focusNode.requestFocus();
                          setState(() {});
                        }
                      },
                      child: Container(
                          child: Text(
                        TIM_t("搜索"),
                        style: TextStyle(
                            color: hexToColor('1DAA61'), fontSize: 13),
                      )))
                ],
              ),
            ),
            if (showResult)
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: SingleChildScrollView(
                    child: Column(
                      children: _isGroup
                          ? _groupBuilder()
                          : _searchResultBuilder(searchResult, theme),
                    ),
                  ),
                ),
              )
          ],
        );
      },
    );
  }
}
