import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/optimize_utils.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_input.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';

class GroupMemberSearchTextField extends TIMUIKitStatelessWidget {
  final Function(String text) onTextChange;

  GroupMemberSearchTextField({Key? key, required this.onTextChange})
      : super(key: key);

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;
    final isDesktopScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
    final FocusNode focusNode = FocusNode();

    var debounceFunc = OptimizeUtils.debounce(
        (text) => onTextChange(text), const Duration(milliseconds: 300));

    return SizedBox(
      child: Column(children: [
        if (!isDesktopScreen)
          Container(
              height: 27,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              margin: const EdgeInsets.only(top: 6, bottom: 8),
              child: TextField(
                autofocus: false,
                onChanged: debounceFunc,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.search,
                textAlignVertical: TextAlignVertical.center,
                textAlign: TextAlign.start,
                style: const TextStyle(fontSize: 12),
                decoration: InputDecoration(
                  enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(9),
                      borderSide: BorderSide.none),
                  focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(9),
                      borderSide: BorderSide.none),
                  contentPadding:  EdgeInsets.all(6),
                  border: const OutlineInputBorder(borderSide: BorderSide.none),
                  hintStyle: TextStyle(
                    fontSize: isDesktopScreen ? 12 : 12,
                    color: hexToColor("646464"),
                  ),
                  fillColor: isDesktopScreen
                      ? hexToColor("f3f3f4")
                      : hexToColor('ECECEC'),
                  filled: true,
                  isDense: true,
                  hintText: TIM_t("搜索"),
                  prefixIconConstraints: const BoxConstraints.tightFor(width: 24),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.only(left: 8, right: 4),
                    child: SvgPicture.asset(
                      'images/search.svg',
                      package: 'tencent_cloud_chat_uikit',
                      width: 11,
                      height: 11,
                    ),
                  ),
                ),
              )),
        if (isDesktopScreen)
          TIMUIKitSearchInput(
            prefixIcon: Icon(
              Icons.search,
              size: 16,
              color: hexToColor("979797"),
            ),
            onChange: (text) {
              focusNode.requestFocus();
              debounceFunc(text);
            },
            focusNode: focusNode,
          ),
      ]),
    );
  }
}
