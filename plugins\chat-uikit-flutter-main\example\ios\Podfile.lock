PODS:
  - audioplayers_darwin (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - disk_space (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.4):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.4)
  - DKImagePickerController/PhotoGallery (4.3.4):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.4)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - fc_native_video_thumbnail_for_us (0.0.1):
    - Flutter
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_image_compress (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_plugin_record_plus (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - HydraAsync (2.0.6)
  - image_gallery_saver (1.5.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - libwebp (1.2.4):
    - libwebp/demux (= 1.2.4)
    - libwebp/mux (= 1.2.4)
    - libwebp/webp (= 1.2.4)
  - libwebp/demux (1.2.4):
    - libwebp/webp
  - libwebp/mux (1.2.4):
    - libwebp/demux
  - libwebp/webp (1.2.4)
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.0.4):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - ReactiveObjC (3.1.1)
  - SDWebImage (5.15.5):
    - SDWebImage/Core (= 5.15.5)
  - SDWebImage/Core (5.15.5)
  - SDWebImageWebPCoder (0.11.0):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.15)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - SwiftyGif (5.4.4)
  - tencent_cloud_chat_sdk (5.1.2):
    - Flutter
    - HydraAsync
    - TXIMSDK_Plus_iOS (= 7.1.3925)
  - tencent_cloud_uikit_core (0.0.1):
    - Flutter
    - TUICore (= 7.1.3925)
  - tencent_open_file (0.0.1):
    - Flutter
  - Toast (4.0.0)
  - TUICore (7.1.3925):
    - ReactiveObjC
    - SDWebImage
    - TUICore/ImSDK_Plus (= 7.1.3925)
  - TUICore/Base (7.1.3925):
    - ReactiveObjC
    - SDWebImage
  - TUICore/ImSDK_Plus (7.1.3925):
    - ReactiveObjC
    - SDWebImage
    - TUICore/Base
    - TXIMSDK_Plus_iOS (= 7.1.3925)
  - TXIMSDK_Plus_iOS (7.1.3925)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - disk_space (from `.symlinks/plugins/disk_space/ios`)
  - fc_native_video_thumbnail_for_us (from `.symlinks/plugins/fc_native_video_thumbnail_for_us/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress (from `.symlinks/plugins/flutter_image_compress/ios`)
  - flutter_plugin_record_plus (from `.symlinks/plugins/flutter_plugin_record_plus/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - tencent_cloud_chat_sdk (from `.symlinks/plugins/tencent_cloud_chat_sdk/ios`)
  - tencent_cloud_uikit_core (from `.symlinks/plugins/tencent_cloud_uikit_core/ios`)
  - tencent_open_file (from `.symlinks/plugins/tencent_open_file/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - FMDB
    - HydraAsync
    - libwebp
    - Mantle
    - ReactiveObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif
    - Toast
    - TUICore
    - TXIMSDK_Plus_iOS

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  disk_space:
    :path: ".symlinks/plugins/disk_space/ios"
  fc_native_video_thumbnail_for_us:
    :path: ".symlinks/plugins/fc_native_video_thumbnail_for_us/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress:
    :path: ".symlinks/plugins/flutter_image_compress/ios"
  flutter_plugin_record_plus:
    :path: ".symlinks/plugins/flutter_plugin_record_plus/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  tencent_cloud_chat_sdk:
    :path: ".symlinks/plugins/tencent_cloud_chat_sdk/ios"
  tencent_cloud_uikit_core:
    :path: ".symlinks/plugins/tencent_cloud_uikit_core/ios"
  tencent_open_file:
    :path: ".symlinks/plugins/tencent_open_file/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  camera_avfoundation: 07c77549ea54ad95d8581be86617c094a46280d9
  disk_space: e94d34bbdf77954adfb39e60bde9cc5c7233eda6
  DKImagePickerController: b512c28220a2b8ac7419f21c491fc8534b7601ac
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  fc_native_video_thumbnail_for_us: 69559e6500bff0f6340f044ec0847366fa6f6233
  file_picker: 817ab1d8cd2da9d2da412a417162deee3500fc95
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_image_compress: 5a5e9aee05b6553048b8df1c3bc456d0afaac433
  flutter_plugin_record_plus: 79b8e13ee7ed9a94f6c067018653599528cee1fc
  fluttertoast: eb263d302cc92e04176c053d2385237e9f43fad0
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  image_gallery_saver: 259eab68fb271cfd57d599904f7acdc7832e7ef2
  image_picker_ios: b786a5dcf033a8336a657191401bfdf12017dabb
  libwebp: f62cb61d0a484ba548448a4bd52aabf150ff6eef
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  package_info_plus: 6c92f08e1f853dc01228d6f553146438dafcd14e
  pasteboard: 982969ebaa7c78af3e6cc7761e8f5e77565d9ce0
  path_provider_foundation: 37748e03f12783f9de2cb2c4eadfaa25fe6d4852
  permission_handler_apple: 44366e37eaf29454a1e7b1b7d736c2cceaeb17ce
  photo_manager: 4f6810b7dfc4feb03b461ac1a70dacf91fba7604
  ReactiveObjC: 011caa393aa0383245f2dcf9bf02e86b80b36040
  SDWebImage: fd7e1a22f00303e058058278639bf6196ee431fe
  SDWebImageWebPCoder: 295a6573c512f54ad2dd58098e64e17dcf008499
  shared_preferences_foundation: 297b3ebca31b34ec92be11acd7fb0ba932c822ca
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  SwiftyGif: 93a1cc87bf3a51916001cf8f3d63835fb64c819f
  tencent_cloud_chat_sdk: 17f2ddd7de43495312603e7c9dac04d76352e246
  tencent_cloud_uikit_core: 2c4ccb41c33b45b5c69750b9774fa389fc20cdb2
  tencent_open_file: 1261db508715b8f43ef3b7e31c90824838038165
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  TUICore: edc9e0911f6a04224620d7098ff9d7f4b1f0291a
  TXIMSDK_Plus_iOS: 3edf95acc3dff794287ea858b5205ed6f4dd339f
  url_launcher_ios: ae1517e5e344f5544fb090b079e11f399dfbe4d2
  video_player_avfoundation: e489aac24ef5cf7af82702979ed16f2a5ef84cff
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f

PODFILE CHECKSUM: 7368163408c647b7eb699d0d788ba6718e18fb8d

COCOAPODS: 1.12.0
