// ignore_for_file: unrelated_type_equality_checks, avoid_print

import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_change_info_type.dart';
import 'package:tencent_cloud_chat_sdk/enum/group_tips_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_change_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_group_change_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_group_member_full_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_group_member_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_tips_elem.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_group_tips_elem.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_image.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_image.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_value_callback.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_value_callback.dart';
import 'package:tencent_cloud_chat_sdk/tencent_im_sdk_plugin.dart';
import 'package:tencent_cloud_chat_uikit/ui/constants/history_message_constant.dart';
import 'package:tencent_cloud_chat_uikit/ui/constants/time.dart';
import 'package:collection/collection.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/common_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/logger.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';

class MessageUtils {
  // 判断CallingData的方式和Trtc的方法一致
  static isCallingData(String data) {
    try {
      Map<String, dynamic> customMap = jsonDecode(data);

      if (customMap.containsKey('businessID') && customMap['businessID'] == 1) {
        return true;
      }
    } catch (e) {
      outputLogger.i("isCallingData json parse error");
      return false;
    }
    return false;
  }

  // 是否是群组TRTC信息
  static isGroupCallingMessage(V2TimMessage message) {
    final isGroup = message.groupID != null;
    final isCustomMessage =
        message.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM;
    if (isCustomMessage) {
      final customElemData = message.customElem?.data ?? "";
      return isCallingData(customElemData) && isGroup;
    }
    return false;
  }

  static String getCustomGroupCreatedOrDismissedString(V2TimMessage message) {
    try {
      final isGroup = message.groupID != null;
      final isCustomMessage =
          message.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM;
      if (isCustomMessage && isGroup) {
        final data = message.customElem?.data ?? "";
        Map<String, dynamic> customMap = jsonDecode(data);
        if (customMap.containsKey('businessID') &&
            customMap['businessID'] == "group_create") {
          final content =
              "${customMap['opUser']}${TIM_t(customMap['content'])}";
          return content;
        }
        return "";
      }
      return "";
    } catch (e) {
      outputLogger.i("getCustomGroupCreatedOrDismissedString json parse error");
      return "";
    }
  }

  /// 抢红包消息
  static bool isCustomRob(V2TimMessage message) {
    try {
      final isCustomMessage =
          message.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM;

      if (isCustomMessage) {
        var elem = jsonDecode(message.customElem?.data ?? '');
        if (elem.containsKey('type') && elem['type'] == "RedBaoTip") {
          return true;
        }
        return false;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// 是否特别关注
  static bool isSpecia(V2TimMessage message) {
    try {
      final isCustomMessage =
          message.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM;

      if (isCustomMessage && message.customElem?.data == 'SPECIA') {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<String> _getGroupChangeType(V2TimGroupChangeInfo info,
      List<V2TimGroupMemberFullInfo?> groupMemberList) async {
    int? type = info.type;
    var value = info.value;
    String s = TIM_t('群资料信息');
    switch (type) {
      case GroupChangeInfoType.V2TIM_GROUP_INFO_CHANGE_TYPE_CUSTOM:
        s = TIM_t("自定义字段");
        break;
      case GroupChangeInfoType.V2TIM_GROUP_INFO_CHANGE_TYPE_FACE_URL:
        s = TIM_t("群头像");
        break;
      case GroupChangeInfoType.V2TIM_GROUP_INFO_CHANGE_TYPE_INTRODUCTION:
        s = TIM_t("群简介");
        break;
      case GroupChangeInfoType.V2TIM_GROUP_INFO_CHANGE_TYPE_NAME:
        s = TIM_t("群名称");
        break;
      case GroupChangeInfoType.V2TIM_GROUP_INFO_CHANGE_TYPE_NOTIFICATION:
        s = TIM_t("群公告");
        break;
      case GroupChangeInfoType.V2TIM_GROUP_INFO_CHANGE_TYPE_OWNER:
        s = TIM_t("群主");
        final V2TimGroupMemberFullInfo? groupMemberInfo = groupMemberList
            .firstWhereOrNull((element) => element?.userID == value);
        if (groupMemberInfo != null) {
          value = TencentUtils.checkString(groupMemberInfo.friendRemark) ??
              TencentUtils.checkString(groupMemberInfo.nameCard) ??
              TencentUtils.checkString(groupMemberInfo.nickName) ??
              TencentUtils.checkString(groupMemberInfo.userID);
        } else {
          final res = await TencentImSDKPlugin.v2TIMManager
              .getUsersInfo(userIDList: [value ?? ""]);
          if (res.code == 0) {
            final List<V2TimUserFullInfo> data = res.data ?? [];
            if (data.isNotEmpty) {
              final firstPerson = data[0];
              value = TencentUtils.checkString(firstPerson.nickName) ??
                  TencentUtils.checkString(firstPerson.userID);
            }
          }
        }
        break;
      case GroupChangeInfoType.V2TIM_GROUP_INFO_CHANGE_TYPE_SHUT_UP_ALL:
        s = TIM_t("全员禁言状态");
        break;
      case GroupChangeInfoType.V2TIM_GROUP_INFO_CHANGE_TYPE_RECEIVE_MESSAGE_OPT:
        s = TIM_t("消息接收方式");
        break;
      case GroupChangeInfoType.V2TIM_GROUP_INFO_CHANGE_TYPE_GROUP_ADD_OPT:
        s = TIM_t("加群方式");
        break;
    }

    final String option8 = s;
    if (value != null && value.isNotEmpty) {
      return TIM_t_para("{{option8}}为 ", "$option8为 ")(option8: option8) +
          value;
    } else {
      return option8;
    }
  }

  static String? _getOpUserNick(V2TimGroupMemberInfo? opUser) {
    if (opUser == null) {
      return "";
    }
    return TencentUtils.checkString(opUser.friendRemark) ??
        TencentUtils.checkString(opUser.nickName) ??
        TencentUtils.checkString(opUser.userID);
  }

  static String? _getMemberNickName(V2TimGroupMemberInfo e) {
    final friendRemark = e.friendRemark;
    final nameCard = e.nameCard;
    final nickName = e.nickName;
    final userID = e.userID;

    if (friendRemark != null && friendRemark != "") {
      return friendRemark;
    } else if (nameCard != null && nameCard != "") {
      return nameCard;
    } else if (nickName != null && nickName != "") {
      return nickName;
    } else {
      return userID;
    }
  }

// 静态方法，用于生成群组提示消息的摘要
  static Future<String> groupTipsMessageAbstract(
      V2TimGroupTipsElem groupTipsElem,
      List<V2TimGroupMemberFullInfo?> groupMemberList) async {
    // 定义要返回的字符串
    String displayMessage;
    // 获取操作类型
    final operationType = groupTipsElem.type;
    // 获取操作成员
    final operationMember = groupTipsElem.opMember;
    // 获取成员列表
    final memberList = groupTipsElem.memberList;
    // 获取操作成员的昵称
    final opUserNickName = _getOpUserNick(operationMember);
    // 根据操作类型进行不同的处理
    switch (operationType) {
      // 群资料修改
      case GroupTipsElemType.V2TIM_GROUP_TIPS_TYPE_GROUP_INFO_CHANGE:
        // 获取操作成员的昵称
        final String? option7 = opUserNickName ?? "";
        // 获取群资料修改信息列表
        final groupChangeInfoList = groupTipsElem.groupChangeInfoList ?? [];
        // 定义修改信息字符串
        String changedInfoString = "";
        // 定义是否修改的标志
        bool changedValue = false;
        // 遍历群资料修改信息列表
        for (V2TimGroupChangeInfo? element in groupChangeInfoList) {
          // 获取修改类型
          final newText = await _getGroupChangeType(element!, groupMemberList);
          // 拼接修改信息字符串
          changedInfoString +=
              (changedInfoString.isEmpty ? "" : " / ") + newText;
          // 设置是否修改的标志
          changedValue = element!.boolValue ?? false;
        }
        // 如果修改信息字符串为空，则设置为群资料
        if (changedInfoString.isEmpty) {
          changedInfoString = TIM_t("群资料");
        }
        // 如果修改信息字符串为全员禁言状态，则设置为全员禁言
        if (changedInfoString == TIM_t("全员禁言状态")) {
          changedInfoString = TIM_t("全员禁言");
          // 根据是否修改的标志设置显示消息
          displayMessage = changedValue == false
              ? TIM_t_para("{{option7}} 取消", "$option7 ${TIM_t("取消")}")(
                      option7: option7) +
                  changedInfoString
              : TIM_t_para("{{option7}} 开启", "$option7 ${TIM_t("开启")}")(
                      option7: option7) +
                  changedInfoString;
        } else {
          // 否则设置显示消息为修改
          displayMessage = TIM_t_para(
                  "{{option7}}修改", "$option7${TIM_t("修改")}")(option7: option7) +
              changedInfoString;
        }
        break;
      // 退出群聊
      case GroupTipsElemType.V2TIM_GROUP_TIPS_TYPE_QUIT:
        // 获取操作成员的昵称
        final String? option6 = opUserNickName ?? "";
        // 设置显示消息为退出群聊
        displayMessage =
            TIM_t_para("{{option6}}退出群聊", "$option6退出群聊")(option6: option6);
        break;
      // 邀请加入群组
      case GroupTipsElemType.V2TIM_GROUP_TIPS_TYPE_INVITE:
        // 获取被邀请成员的昵称
        final option5 =
            memberList!.map((e) => _getMemberNickName(e!).toString()).join("、");
        // 获取操作成员的昵称
        final inviteUser = _getOpUserNick(operationMember);
        // 设置显示消息为邀请加入群组
        displayMessage = '$inviteUser' +
            TIM_t_para("邀请{{option5}}加入群组", "邀请$option5加入群组")(option5: option5);
        break;
      // 踢出群组
      case GroupTipsElemType.V2TIM_GROUP_TIPS_TYPE_KICKED:
        // 获取被踢成员的昵称
        final option4 =
            memberList!.map((e) => _getMemberNickName(e!).toString()).join("、");
        // 获取操作成员的昵称
        final kickUser = _getOpUserNick(operationMember);
        // 设置显示消息为将成员踢出群组
        displayMessage = '$kickUser' +
            TIM_t_para("将{{option4}}踢出群组", "将$option4踢出群组")(option4: option4);
        break;
      // 加入群聊
      case GroupTipsElemType.V2TIM_GROUP_TIPS_TYPE_JOIN:
        // 获取加入成员的昵称
        final option3 =
            memberList!.map((e) => _getMemberNickName(e!).toString()).join("、");
        // 设置显示消息为加入群聊
        displayMessage = TIM_t_para("用户{{option3}}加入了群聊", "用户$option3加入了群聊")(
            option3: option3);
        break;
      // 成员信息修改
      case GroupTipsElemType.V2TIM_GROUP_TIPS_TYPE_MEMBER_INFO_CHANGE:
        // 获取修改成员的昵称
        displayMessage = groupTipsElem.memberList!.map((e) {
          // 获取修改成员的信息
          final changedMember = groupTipsElem.memberChangeInfoList!
              .firstWhere((element) => element!.userID == e!.userID);
          // 判断是否禁言
          final isMute = changedMember!.muteTime != 0;
          // 获取修改成员的昵称
          final option2 = _getMemberNickName(e!);
          // 设置显示消息为禁言或解除禁言
          final displayMessage = isMute ? TIM_t("禁言") : TIM_t("解除禁言");
          return TIM_t_para("{{option2}} 被", "$option2 被")(option2: option2) +
              displayMessage;
        }).join("、");
        break;
      // 设置管理员
      case GroupTipsElemType.V2TIM_GROUP_TIPS_TYPE_SET_ADMIN:
        // 获取被设置管理员的昵称
        final adminMember =
            memberList!.map((e) => _getMemberNickName(e!).toString()).join("、");
        // 获取操作成员的昵称
        final opMember = _getOpUserNick(operationMember);
        // 获取被设置管理员的昵称
        final option1 = adminMember;
        // 设置显示消息为将成员设置为管理员
        displayMessage = '$opMember' +
            TIM_t_para("将 {{option1}} 设置为管理员", "将 $option1 设置为管理员")(
                option1: option1);
        break;
      // 取消管理员
      case GroupTipsElemType.V2TIM_GROUP_TIPS_TYPE_CANCEL_ADMIN:
        // 获取被取消管理员的昵称
        final adminMember =
            memberList!.map((e) => _getMemberNickName(e!).toString()).join("、");
        // 获取操作成员的昵称
        final opMember = _getOpUserNick(operationMember);
        // 获取被取消管理员的昵称
        final option1 = adminMember;
        // 设置显示消息为将成员取消管理员
        displayMessage = '$opMember' +
            TIM_t_para("将 {{option1}} 取消管理员", "将 $option1 取消管理员")(
                option1: option1);
        break;
      // 默认情况
      default:
        // 获取操作类型的字符串
        final String option2 = operationType.toString();
        // 设置显示消息为系统消息
        displayMessage =
            TIM_t_para("系统消息 {{option2}}", "系统消息 $option2")(option2: option2);
        break;
    }
    // 返回显示消息
    return displayMessage;
  }

  static String formatVideoTime(int time) {
    List<int> times = [];
    if (time <= 0) return '0:01';
    if (time >= TimeConst.DAY_SEC) return '1d+';
    for (int idx = 0; idx < TimeConst.SEC_SERIES.length; idx++) {
      int sec = TimeConst.SEC_SERIES[idx];
      if (time >= sec) {
        times.add((time / sec).floor());
        time = time % sec;
      } else if (idx > 0) {
        times.add(0);
      }
    }
    times.add(time);
    String formatTime = times[0].toString();
    for (int idx = 1; idx < times.length; idx++) {
      if (times[idx] < 10) {
        formatTime += ':0${times[idx].toString()}';
      } else {
        formatTime += ':${times[idx].toString()}';
      }
    }
    return formatTime;
  }

  static String handleCustomMessageString(V2TimMessage message) {
    return TIM_t("消息");
  }

  static Widget wrapMessageTips(Widget child, TUITheme? theme) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4),
      child: child,
    );
  }

  static String getAbstractMessageAsync(
      V2TimMessage message, List<V2TimGroupMemberFullInfo?> groupMemberList) {
    final msgType = message.elemType;
    switch (msgType) {
      case MessageElemType.V2TIM_ELEM_TYPE_CUSTOM:
        return handleCustomMessageString(message);
      case MessageElemType.V2TIM_ELEM_TYPE_SOUND:
        return TIM_t("[语音]");
      case MessageElemType.V2TIM_ELEM_TYPE_TEXT:
        return message.textElem!.text as String;
      case MessageElemType.V2TIM_ELEM_TYPE_FACE:
        return TIM_t("[表情]");
      case MessageElemType.V2TIM_ELEM_TYPE_FILE:
        final String? option2 = message.fileElem!.fileName ?? "";
        return TIM_t_para("[文件] {{option2}}", "[文件] $option2")(
            option2: option2);
      case MessageElemType.V2TIM_ELEM_TYPE_GROUP_TIPS:
        return TIM_t("群提示");
      case MessageElemType.V2TIM_ELEM_TYPE_IMAGE:
        return TIM_t("[图片]");
      case MessageElemType.V2TIM_ELEM_TYPE_VIDEO:
        return TIM_t("[视频]");
      case MessageElemType.V2TIM_ELEM_TYPE_LOCATION:
        return TIM_t("[位置]");
      case MessageElemType.V2TIM_ELEM_TYPE_MERGER:
        return TIM_t("[聊天记录]");
      default:
        return TIM_t("未知消息");
    }
  }

  static V2TimImage? getImageFromImgList(
      List<V2TimImage?>? list, List<String> order) {
    V2TimImage? img;
    try {
      for (String type in order) {
        img = list?.firstWhere(
            (e) =>
                e?.type == HistoryMessageDartConstant.V2_TIM_IMAGE_TYPES[type],
            orElse: () => null);
      }
    } catch (e) {
      outputLogger.i('getImageFromImgList error ${e.toString()}');
    }
    return img;
  }

  static String getDisplayName(V2TimMessage message) {
    final friendRemark = message.friendRemark ?? "";
    final nameCard = message.nameCard ?? "";
    final nickName = message.nickName ?? "";
    final sender = message.sender ?? "";
    final displayName = friendRemark.isNotEmpty
        ? friendRemark
        : nameCard.isNotEmpty
            ? nameCard
            : nickName.isNotEmpty
                ? nickName
                : sender;
    return displayName.toString();
  }

  static Future<V2TimValueCallback<V2TimMessage>?> handleMessageError(
      Future<V2TimValueCallback<V2TimMessage>?> fun,
      BuildContext context) async {
    final res = await fun;
    return handleMessageErrorCode(res, context);
  }

  static V2TimValueCallback<V2TimMessage>? handleMessageErrorCode(
      V2TimValueCallback<V2TimMessage>? sendMsgRes, BuildContext context) {
    if (sendMsgRes == null) return null;

    return sendMsgRes;
  }
}
